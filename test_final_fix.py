#!/usr/bin/env python3
"""
Test final pour vérifier que le problème data:, est résolu
"""

import time
from blsFacilateWork import BlsFacilateWork

def test_final_fix():
    """
    Test final avec les vraies conditions du programme principal
    """
    print("=== TEST FINAL DE LA CORRECTION ===")
    
    try:
        # Créer une instance comme dans main.py
        bls = BlsFacilateWork()
        
        print(f"✅ all_manual par défaut: {bls.all_manual}")
        print(f"✅ fast_mode par défaut: {bls.fast_mode}")
        
        # Simuler les données comme dans main.py
        test_email = "<EMAIL>"
        test_data = ["test_id", "<EMAIL>", "test_password", "place", "member", "carte", "cvv", "nom"]
        
        # Ajouter aux structures
        bls.data = {test_email: test_data}
        bls.Cookies = {test_email: None}
        
        print(f"✅ Données configurées pour {test_email}")
        
        # Simuler l'appel exact de main.py (ligne 378)
        print("🚀 Simulation de l'appel exact de main.py...")
        print("   threading.Thread(target=user_session.make_session, args=(index, False))")
        
        # Appel direct comme dans main.py
        bls.make_session(test_email, False)
        
        print("✅ make_session terminé sans erreur!")
        print("🎉 Le problème data:, devrait être résolu!")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_final_fix()
