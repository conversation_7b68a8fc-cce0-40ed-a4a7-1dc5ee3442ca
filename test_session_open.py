# -*- coding: utf-8 -*-
"""
Test pour vérifier l'ouverture des sessions BLS
"""

import sys
import os
import json
import time

def test_session_opening():
    """Test d'ouverture de session"""
    print("🔍 Test d'ouverture de session BLS...")
    
    try:
        # Importer les modules nécessaires
        from blsFacilateWork import BlsFacilateWork
        
        # Créer une instance
        bls = BlsFacilateWork()
        print("✅ Instance BlsFacilateWork créée")
        
        # Vérifier les données utilisateur
        if not bls.data:
            print("❌ Aucune donnée utilisateur trouvée dans data.json")
            return False
        
        # Prendre le premier utilisateur pour le test
        test_user = list(bls.data.keys())[0]
        print(f"🧪 Test avec l'utilisateur: {test_user}")
        
        # Vérifier la structure des données
        user_data = bls.data[test_user]
        if len(user_data) < 8:
            print(f"❌ Structure de données incomplète pour {test_user}")
            return False
        
        print(f"✅ Données utilisateur valides: {len(user_data)} éléments")
        
        # Test de la méthode make_session (sans vraiment ouvrir Chrome)
        print("🔧 Test de la configuration Chrome...")
        
        # Vérifier que les méthodes nécessaires existent
        required_methods = ['make_session', 'open_bls']
        for method in required_methods:
            if hasattr(bls, method):
                print(f"  ✅ Méthode {method} disponible")
            else:
                print(f"  ❌ Méthode {method} manquante")
                return False
        
        # Vérifier la configuration des cookies
        if hasattr(bls, 'Cookies'):
            print(f"✅ Configuration cookies disponible")
            if test_user in bls.Cookies:
                print(f"  ✅ Cookies pour {test_user}: {type(bls.Cookies[test_user])}")
            else:
                print(f"  ⚠️  Pas de cookies pour {test_user}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_url_navigation():
    """Test de navigation URL"""
    print("\n🌐 Test de navigation URL...")
    
    try:
        # Test avec selenium-wire
        from seleniumwire import webdriver
        from selenium.webdriver.chrome.options import Options
        
        print("✅ Modules selenium importés")
        
        # Configuration Chrome pour test
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # Mode sans interface pour le test
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        print("🔧 Configuration Chrome pour test...")
        
        # Créer une instance Chrome temporaire
        driver = webdriver.Chrome(options=chrome_options)
        print("✅ Driver Chrome créé")
        
        # Test de navigation vers BLS
        test_urls = [
            "https://algeria.blsspainglobal.com",
            "https://algeria.blsspainglobal.com/DZA/account/login"
        ]
        
        for url in test_urls:
            try:
                print(f"🔗 Test navigation vers: {url}")
                driver.get(url)
                time.sleep(2)
                
                current_url = driver.current_url
                print(f"  📍 URL actuelle: {current_url}")
                
                if current_url != "data:,":
                    print(f"  ✅ Navigation réussie")
                else:
                    print(f"  ❌ Navigation échouée - reste sur data:,")
                    
            except Exception as url_error:
                print(f"  ❌ Erreur navigation {url}: {url_error}")
        
        # Fermer le driver
        driver.quit()
        print("✅ Driver fermé")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test de navigation: {e}")
        return False

def diagnose_data_url_issue():
    """Diagnostic du problème data:,"""
    print("\n🔍 DIAGNOSTIC DU PROBLÈME data:,")
    print("=" * 50)
    
    print("📋 Causes possibles du problème data:,:")
    print("  1. Erreur de syntaxe dans webdriver.Chrome")
    print("  2. Problème de configuration Chrome")
    print("  3. Problème de réseau/proxy")
    print("  4. Cookies mal configurés")
    print("  5. URL BLS inaccessible")
    
    print("\n🔧 Solutions appliquées:")
    print("  ✅ Correction de 'self. webdriver.Chrome' → 'self.webdriver.Chrome'")
    print("  ✅ Amélioration de la gestion d'erreurs dans open_bls")
    print("  ✅ Navigation directe vers login au lieu de logo.png")
    print("  ✅ Ajout de logs pour le débogage")
    
    print("\n📝 Recommandations:")
    print("  1. Vérifier la connexion internet")
    print("  2. Tester manuellement l'URL: https://algeria.blsspainglobal.com")
    print("  3. Vérifier les paramètres proxy/firewall")
    print("  4. Relancer main.py et tester à nouveau")

def main():
    """Fonction principale"""
    print("🧪 TEST D'OUVERTURE DE SESSION BLS")
    print("=" * 50)
    
    # Vérifier les fichiers requis
    required_files = ['data.json', 'blsFacilateWork.py']
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ Fichier requis manquant: {file}")
            return False
    
    print("✅ Fichiers requis présents")
    
    # Exécuter les tests
    tests = [
        ("Configuration BLS", test_session_opening),
        ("Navigation URL", test_url_navigation)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"💥 Erreur inattendue: {e}")
            results.append((test_name, False))
    
    # Diagnostic
    diagnose_data_url_issue()
    
    # Résumé
    print("\n" + "=" * 50)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHOUÉ"
        print(f"{test_name:.<30} {status}")
    
    print("-" * 50)
    print(f"Total: {passed}/{total} tests réussis")
    
    if passed == total:
        print("\n🎉 Configuration OK!")
        print("✅ Le problème data:, devrait être résolu")
        print("🔧 Relancez main.py et testez l'ouverture de session")
    else:
        print("\n⚠️  Problèmes détectés")
        print("🔧 Vérifiez les erreurs ci-dessus")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
