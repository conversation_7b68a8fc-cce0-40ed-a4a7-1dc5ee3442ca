#!/usr/bin/env python3
"""
Diagnostic approfondi du problème data:,
"""

import time
from blsFacilateWork import BlsFacilateWork

def debug_data_problem():
    """
    Test de diagnostic pour identifier exactement où le problème data:, se produit
    """
    print("=== DIAGNOSTIC DU PROBLÈME data:, ===")
    
    try:
        # Créer une instance de BlsFacilateWork
        bls = BlsFacilateWork()
        
        # Configuration de test
        bls.all_manual = True  # Mode manuel
        bls.fast_mode = False
        
        # Données de test simples
        test_user_data = [
            "test_user_id",
            "<EMAIL>",
            "test_password"
        ]
        
        # Ajouter les données de test
        bls.data = {0: test_user_data}
        bls.Cookies = {0: None}
        
        print("✅ Configuration terminée")
        print(f"📋 all_manual: {bls.all_manual}")
        print(f"📋 fast_mode: {bls.fast_mode}")
        
        # Créer le driver manuellement pour plus de contrôle
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        chrome_options = Options()
        chrome_options.add_experimental_option("excludeSwitches", ['enable-automation'])
        chrome_options.add_argument("--disable-gpu")
        
        print("🚀 Création du driver Chrome...")
        driver = webdriver.Chrome(options=chrome_options)
        
        print(f"🔍 URL initiale du driver: {driver.current_url}")
        
        # Test 1: Navigation directe
        print("\n=== TEST 1: Navigation directe ===")
        driver.get('https://algeria.blsspainglobal.com/DZA/')
        time.sleep(3)
        print(f"✅ URL après navigation directe: {driver.current_url}")
        
        # Test 2: Appeler la méthode open_bls directement
        print("\n=== TEST 2: Appel de open_bls ===")
        try:
            # Simuler l'appel de open_bls
            bls.open_bls(driver, 0, auto=False)
            print(f"✅ URL après open_bls: {driver.current_url}")
        except Exception as e:
            print(f"❌ Erreur dans open_bls: {e}")
            import traceback
            traceback.print_exc()
        
        # Test 3: Vérifier les conditions dans open_bls
        print("\n=== TEST 3: Vérification des conditions ===")
        print(f"📋 bls.all_manual = {bls.all_manual}")
        print(f"📋 bls.fast_mode = {bls.fast_mode}")
        print(f"📋 auto = False")
        
        # Simuler la logique de open_bls
        if bls.all_manual:
            print("✅ Condition all_manual est vraie")
            print("🚀 Tentative de navigation vers algeria.blsspainglobal.com/DZA/")
            driver.get('https://algeria.blsspainglobal.com/DZA/')
            time.sleep(3)
            print(f"✅ URL finale: {driver.current_url}")
        else:
            print("❌ Condition all_manual est fausse")
        
        # Test 4: Vérifier si le problème vient d'ailleurs
        print("\n=== TEST 4: Test avec make_session ===")
        try:
            # Fermer le driver actuel
            driver.quit()
            
            # Utiliser make_session comme dans le vrai programme
            print("🚀 Appel de make_session...")
            bls.make_session(0, auto=False)
            print("✅ make_session terminé")
            
        except Exception as e:
            print(f"❌ Erreur dans make_session: {e}")
            import traceback
            traceback.print_exc()
        
        print("\n=== DIAGNOSTIC TERMINÉ ===")
        
    except Exception as e:
        print(f"❌ Erreur générale: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_data_problem()
