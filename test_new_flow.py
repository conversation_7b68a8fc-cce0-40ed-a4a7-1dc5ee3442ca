#!/usr/bin/env python3
"""
Script de test pour le nouveau flux BLS selon les captures d'écran
"""

import sys
import time
from blsFacilateWork import BlsFacilateWork

def test_new_bls_flow():
    """
    Test du nouveau flux BLS avec les vraies URLs
    """
    print("=== Test du nouveau flux BLS ===")
    
    try:
        # Créer une instance de BlsFacilateWork
        bls = BlsFacilateWork()
        
        # Configuration de test
        bls.all_manual = True  # Mode manuel pour voir ce qui se passe
        bls.fast_mode = False
        
        # Données de test (vous pouvez modifier avec vos vraies données)
        test_user_data = [
            "test_user_id",
            "<EMAIL>",  # Email selon vos captures
            "votre_mot_de_passe"        # Remplacez par le vrai mot de passe
        ]
        
        # Ajouter les données de test
        bls.data = {0: test_user_data}
        bls.Cookies = {0: None}

        # Initialiser les structures nécessaires pour le nouvel utilisateur
        bls.session_chrome[0] = None
        bls.captcha_data[0] = None
        bls.visa_category[0] = None
        bls.all_date[0] = False
        bls.frame_parser[0] = None
        
        print("Configuration terminée. Démarrage du test...")
        
        # Tester la session
        bls.make_session(0, auto=False)
        
        print("Test terminé.")
        
    except Exception as e:
        print(f"Erreur pendant le test: {e}")
        import traceback
        traceback.print_exc()

def test_url_navigation():
    """
    Test simple de navigation vers les bonnes URLs
    """
    print("=== Test de navigation URL ===")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        # Configuration Chrome
        chrome_options = Options()
        chrome_options.add_experimental_option("excludeSwitches", ['enable-automation'])
        # chrome_options.add_argument("--headless")  # Décommentez pour mode headless
        chrome_options.add_argument("--disable-gpu")
        
        # Créer le driver
        driver = webdriver.Chrome(options=chrome_options)
        
        print("Navigation vers la page d'accueil BLS...")
        driver.get('https://algeria.blsspainglobal.com/DZA/bls/vtv9850')
        
        print(f"URL actuelle: {driver.current_url}")
        print(f"Titre de la page: {driver.title}")
        
        # Attendre un peu pour voir la page
        time.sleep(5)
        
        # Vérifier si on peut trouver le bouton "Book Now" ou "Try Again"
        try:
            book_now_elements = driver.find_elements("xpath", "//button[contains(text(), 'Book Now')]")
            try_again_elements = driver.find_elements("xpath", "//button[contains(text(), 'Try Again')]")
            
            if book_now_elements:
                print("✓ Bouton 'Book Now' trouvé")
            elif try_again_elements:
                print("✓ Bouton 'Try Again' trouvé")
            else:
                print("⚠ Aucun bouton de réservation trouvé")
                
        except Exception as e:
            print(f"Erreur lors de la recherche des boutons: {e}")
        
        print("Fermeture du navigateur dans 10 secondes...")
        time.sleep(10)
        driver.quit()
        
        print("Test de navigation terminé.")
        
    except Exception as e:
        print(f"Erreur pendant le test de navigation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Choisissez le test à exécuter:")
    print("1. Test complet du nouveau flux")
    print("2. Test simple de navigation URL")
    
    choice = input("Votre choix (1 ou 2): ").strip()
    
    if choice == "1":
        test_new_bls_flow()
    elif choice == "2":
        test_url_navigation()
    else:
        print("Choix invalide. Exécution du test de navigation par défaut.")
        test_url_navigation()
