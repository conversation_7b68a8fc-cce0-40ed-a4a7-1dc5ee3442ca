#!/usr/bin/env python3
"""
Test pour vérifier que les données de mot de passe sont bien récupérées
"""

import json
from blsFacilateWork import BlsFacilateWork

def test_password_data():
    """
    Test pour vérifier la récupération des données de mot de passe
    """
    print("=== TEST DES DONNÉES DE MOT DE PASSE ===")
    
    try:
        # Charger les données directement depuis le fichier
        with open('data.json', 'r') as f:
            data = json.load(f)
        
        user = "<EMAIL>"
        
        print(f"📋 Données pour {user}:")
        if user in data:
            user_data = data[user]
            print(f"   Index 0 (Email): {user_data[0]}")
            print(f"   Index 1 (Password): {user_data[1]}")
            print(f"   Index 2 (Place): {user_data[2]}")
            print(f"   Index 3 (Member): {user_data[3]}")
            
            # Vérifier que le mot de passe n'est pas vide
            if user_data[1]:
                print(f"✅ Mot de passe trouvé: {len(user_data[1])} caractères")
            else:
                print("❌ Mot de passe vide!")
        else:
            print(f"❌ Utilisateur {user} non trouvé dans data.json")
        
        # Tester avec BlsFacilateWork
        print("\n📋 Test avec BlsFacilateWork:")
        bls = BlsFacilateWork()
        
        if user in bls.data:
            user_password = bls.data[user][1] if isinstance(bls.data[user], list) else "default_password"
            print(f"   Mot de passe récupéré: {len(user_password)} caractères")
            print(f"   Mot de passe commence par: {user_password[:3]}...")
            print(f"✅ Récupération réussie via BlsFacilateWork")
        else:
            print(f"❌ Utilisateur {user} non trouvé dans bls.data")
            
        # Simuler la logique de récupération du mot de passe
        print("\n📋 Simulation de la logique de saisie:")
        test_user = user
        if test_user in bls.data and isinstance(bls.data[test_user], list) and len(bls.data[test_user]) > 1:
            password = bls.data[test_user][1]
            print(f"   Mot de passe qui serait saisi: {password}")
            print(f"✅ Logique de récupération fonctionnelle")
        else:
            print("❌ Problème dans la logique de récupération")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_password_data()
