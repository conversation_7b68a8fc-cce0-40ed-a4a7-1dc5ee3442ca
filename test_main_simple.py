#!/usr/bin/env python3
"""
Test simple pour vérifier que main.py fonctionne
"""

import subprocess
import sys
import time

def test_main_simple():
    """
    Test simple de main.py
    """
    print("=== TEST SIMPLE DE MAIN.PY ===")
    
    try:
        print("🚀 Lancement de main.py...")
        
        # Lancer main.py dans un processus séparé
        process = subprocess.Popen(
            [sys.executable, "main.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd="C:\\Users\\<USER>\\Desktop\\BOT-BLS-ESPAGNE--main"
        )
        
        # Attendre un peu pour voir les premiers logs
        time.sleep(10)
        
        # Lire les premiers logs
        try:
            stdout, stderr = process.communicate(timeout=5)
            print("📋 STDOUT:")
            print(stdout[:1000] if stdout else "Aucun stdout")
            print("\n📋 STDERR:")
            print(stderr[:1000] if stderr else "Aucun stderr")
        except subprocess.TimeoutExpired:
            print("⏱️ Processus encore en cours d'exécution")
            print("📋 Interface graphique probablement lancée")
            
            # Terminer le processus
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
        
        print("✅ Test terminé")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_main_simple()
