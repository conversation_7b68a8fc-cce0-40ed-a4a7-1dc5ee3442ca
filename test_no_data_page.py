#!/usr/bin/env python3
"""
Test pour vérifier que la page "data:" est VRAIMENT éliminée
"""

import time
from blsFacilateWork import BlsFacilateWork

def test_no_data_page():
    """
    Test pour vérifier que Chrome démarre DIRECTEMENT sur l'URL sans "data:"
    """
    print("=== TEST ÉLIMINATION PAGE DATA: ===")
    
    try:
        print("🎯 OBJECTIF: Chrome démarre DIRECTEMENT sur l'URL de connexion")
        print("🔥 SOLUTION: --app=URL pour forcer démarrage direct")
        print("❌ PROBLÈME À RÉSOUDRE: Plus jamais de page 'data:' !")
        
        # Créer une instance
        bls = BlsFacilateWork()
        
        # Données du compte
        test_email = "<EMAIL>"
        test_password = "Karim1978"
        test_data = [
            test_email,        # Index 0: Email
            test_password,     # Index 1: Password  
            "ALGER",          # Index 2: Place
            "3",              # Index 3: Member
            "",               # Index 4: N carte
            "",               # Index 5: CVV
            "",               # Index 6: Nom Carte
            ""                # Index 7: Expiry
        ]
        
        bls.data = {test_email: test_data}
        bls.Cookies = {test_email: None}
        
        print(f"✅ Compte configuré: {test_email}")
        print("🚀 Démarrage du test ÉLIMINATION DATA:...")
        print("📋 Solution --app=URL:")
        print("   🔧 chrome_options.add_argument(f'--app={login_url}')")
        print("   ⚡ Chrome démarre DIRECTEMENT sur l'URL")
        print("   🚫 Plus de page 'data:' intermédiaire")
        print("   ✅ Plus de navigation redondante")
        
        # Démarrer le processus
        start_time = time.perf_counter()
        
        print(f"\n⏱️  Démarrage à {time.strftime('%H:%M:%S')}")
        print("🔍 VÉRIFICATIONS CRITIQUES:")
        print("   1. 🚀 Chrome s'ouvre-t-il DIRECTEMENT sur l'URL ? (OUI/NON)")
        print("   2. ❌ Y a-t-il une page 'data:' au début ? (NON attendu)")
        print("   3. ⚡ L'URL de connexion apparaît-elle immédiatement ? (OUI attendu)")
        print("   4. 📧 L'email est-il injecté rapidement ? (OUI attendu)")
        
        print("\n🔍 SURVEILLEZ LE NAVIGATEUR:")
        print("   👀 Regardez la barre d'adresse dès l'ouverture")
        print("   ✅ Elle doit afficher IMMÉDIATEMENT l'URL BLS")
        print("   ❌ Elle ne doit JAMAIS afficher 'data:' ou page vide")
        
        # Démarrer le processus
        bls.make_session(test_email, False)
        
        end_time = time.perf_counter()
        duration = end_time - start_time
        
        print(f"\n⏱️  Fin à {time.strftime('%H:%M:%S')}")
        print(f"⚡ Test terminé en {duration:.2f} secondes!")
        
        # Demander confirmation à l'utilisateur
        print("\n🔍 VÉRIFICATION MANUELLE NÉCESSAIRE:")
        print("   ❓ Avez-vous vu une page 'data:' au début ? (Répondez dans le chat)")
        print("   ❓ Chrome a-t-il démarré directement sur l'URL BLS ? (Répondez dans le chat)")
        print("   ❓ L'email a-t-il été injecté rapidement ? (Répondez dans le chat)")
        
        # Analyse automatique
        if duration < 20:
            print("🏆 TEMPS EXCELLENT - Potentiellement résolu ! (< 20s)")
        elif duration < 30:
            print("✅ TEMPS BON - Amélioration visible (< 30s)")
        elif duration < 45:
            print("⚠️  TEMPS MOYEN - Encore des optimisations (< 45s)")
        else:
            print("❌ TEMPS LENT - Problème non résolu (> 45s)")
        
        print("\n📊 DIAGNOSTIC TECHNIQUE:")
        print(f"   ⏱️  Durée: {duration:.2f} secondes")
        print("   🔧 Solution --app=URL appliquée")
        print("   ⚡ Navigation redondante supprimée")
        print("   🎯 Injection directe activée")
        
        print("\n💡 SI LE PROBLÈME PERSISTE:")
        print("   🔧 Essayer --kiosk au lieu de --app")
        print("   🔧 Utiliser --new-window avec URL")
        print("   🔧 Mode headless (navigateur invisible)")
        print("   🔧 Selenium Grid pour plus de contrôle")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

def show_data_elimination_strategy():
    """
    Explique la stratégie d'élimination de la page data:
    """
    print("=== STRATÉGIE ÉLIMINATION PAGE DATA: ===")
    
    print("\n🔥 PROBLÈME IDENTIFIÉ:")
    print("   ❌ Chrome démarre TOUJOURS sur 'data:' (page vide)")
    print("   ❌ Puis navigue vers l'URL avec chrome.get()")
    print("   ❌ Perte de 2-5 secondes à chaque fois")
    print("   ❌ Page intermédiaire inutile")
    
    print("\n✅ SOLUTION --app=URL:")
    print("   🚀 chrome_options.add_argument(f'--app={url}')")
    print("   ⚡ Chrome démarre DIRECTEMENT sur l'URL")
    print("   🚫 Pas de page 'data:' intermédiaire")
    print("   ✅ Gain de 2-5 secondes immédiat")
    
    print("\n🔧 TECHNIQUE:")
    print("   1. Construire l'URL complète de connexion")
    print("   2. Ajouter --app=URL aux options Chrome")
    print("   3. Supprimer chrome.get() redondant")
    print("   4. Injection immédiate dès ouverture")
    
    print("\n🎯 RÉSULTAT ATTENDU:")
    print("   ✅ Chrome s'ouvre directement sur page de connexion")
    print("   ✅ Plus de page 'data:' visible")
    print("   ✅ Email injecté en 1-2 secondes")
    print("   ✅ Processus complet en 15-20 secondes")

if __name__ == "__main__":
    show_data_elimination_strategy()
    print("\n" + "="*50 + "\n")
    test_no_data_page()
