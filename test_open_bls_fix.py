# -*- coding: utf-8 -*-
"""
Test spécifique pour vérifier la correction du problème data:,
"""

import sys
import os
import time

def test_open_bls_logic():
    """Test de la logique de open_bls"""
    print("🔍 Test de la logique open_bls...")
    
    try:
        from blsFacilateWork import BlsFacilateWork
        
        # Créer une instance
        bls = BlsFacilateWork()
        print("✅ Instance BlsFacilateWork créée")
        
        # Vérifier les données
        if not bls.data:
            print("❌ Aucune donnée utilisateur")
            return False
        
        test_user = list(bls.data.keys())[0]
        print(f"🧪 Test avec: {test_user}")
        
        # Examiner le code de open_bls
        import inspect
        source = inspect.getsource(bls.open_bls)
        
        # Vérifications importantes
        checks = [
            ("Navigation chrome.get présente", "chrome.get(" in source),
            ("Gestion is_open correcte", "is_open = True" in source),
            ("Gestion auto parameter", "if not auto" in source),
            ("URL BLS présente", "algeria.blsspainglobal.com" in source)
        ]
        
        all_good = True
        for check_name, condition in checks:
            if condition:
                print(f"  ✅ {check_name}")
            else:
                print(f"  ❌ {check_name}")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def simulate_session_opening():
    """Simulation d'ouverture de session"""
    print("\n🎭 Simulation d'ouverture de session...")
    
    try:
        from blsFacilateWork import BlsFacilateWork
        
        # Créer une instance
        bls = BlsFacilateWork()
        
        # Prendre le premier utilisateur
        test_user = list(bls.data.keys())[0]
        
        # Simuler les conditions d'ouverture manuelle
        print(f"📋 Conditions de test:")
        print(f"  • Utilisateur: {test_user}")
        print(f"  • Mode auto: False (clic sur Open)")
        print(f"  • all_manual: {bls.all_manual}")
        print(f"  • fast_mode: {bls.fast_mode}")
        
        # Créer un mock chrome pour tester la logique
        class MockChrome:
            def __init__(self):
                self.current_url = "data:,"
                self.navigation_calls = []
            
            def get(self, url):
                print(f"    🌐 Navigation vers: {url}")
                self.navigation_calls.append(url)
                self.current_url = url
            
            def add_cookie(self, cookie):
                print(f"    🍪 Ajout cookie: {type(cookie)}")
        
        mock_chrome = MockChrome()
        
        # Test de la logique sans vraiment ouvrir Chrome
        print("\n🔧 Test de la logique de navigation:")
        
        # Simuler les conditions
        auto = False  # Clic sur "Open"
        user = test_user
        
        # Logique simplifiée de open_bls
        is_open = False
        navigation_attempted = False
        
        while not is_open:
            print("  📍 Début de la boucle while")
            
            if bls.all_manual:
                print("  🔧 Mode all_manual activé")
                mock_chrome.get('https://algeria.blsspainglobal.com/DZA/account/login')
                navigation_attempted = True
                if not auto:
                    is_open = True
                break
            
            if bls.fast_mode:
                print("  ⚡ Mode fast_mode activé")
                mock_chrome.get('https://algeria.blsspainglobal.com/assets/images/logo.png')
                if bls.Cookies[user] is not None:
                    mock_chrome.add_cookie(bls.Cookies[user])
                mock_chrome.get('https://algeria.blsspainglobal.com/DZA/bls/vtv9850')
                navigation_attempted = True
                if not auto:
                    is_open = True
            else:
                print("  🌐 Mode normal activé")
                mock_chrome.get('https://algeria.blsspainglobal.com/DZA/account/login')
                time.sleep(0.1)  # Simuler l'attente
                if bls.Cookies[user] is not None:
                    mock_chrome.add_cookie(bls.Cookies[user])
                mock_chrome.get('https://algeria.blsspainglobal.com/DZA/bls/vtv9850')
                navigation_attempted = True
                if not auto:
                    is_open = True
        
        print(f"\n📊 Résultats de la simulation:")
        print(f"  • Navigation tentée: {navigation_attempted}")
        print(f"  • Boucle terminée: {is_open}")
        print(f"  • URL finale: {mock_chrome.current_url}")
        print(f"  • Appels de navigation: {len(mock_chrome.navigation_calls)}")
        
        for i, url in enumerate(mock_chrome.navigation_calls, 1):
            print(f"    {i}. {url}")
        
        # Vérifier que la navigation a eu lieu
        if navigation_attempted and mock_chrome.current_url != "data:,":
            print("✅ Simulation réussie - Navigation effectuée")
            return True
        else:
            print("❌ Simulation échouée - Pas de navigation")
            return False
        
    except Exception as e:
        print(f"❌ Erreur simulation: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_fix_summary():
    """Affiche le résumé des corrections"""
    print("\n🔧 RÉSUMÉ DES CORRECTIONS APPLIQUÉES")
    print("=" * 50)
    
    print("❌ Problème identifié:")
    print("  • is_open = True était défini AVANT la navigation")
    print("  • La boucle while se terminait sans naviguer")
    print("  • Le navigateur restait sur data:,")
    
    print("\n✅ Corrections appliquées:")
    print("  • Déplacé is_open = True APRÈS la navigation")
    print("  • Ajouté is_open = True dans toutes les branches")
    print("  • Navigation garantie avant sortie de boucle")
    
    print("\n🎯 Résultat attendu:")
    print("  • Navigation vers algeria.blsspainglobal.com")
    print("  • Plus de problème data:,")
    print("  • Ouverture de session fonctionnelle")

def main():
    """Fonction principale"""
    print("🧪 TEST DE CORRECTION DU PROBLÈME data:,")
    print("=" * 50)
    
    tests = [
        ("Logique open_bls", test_open_bls_logic),
        ("Simulation ouverture", simulate_session_opening)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"💥 Erreur: {e}")
            results.append((test_name, False))
    
    # Afficher le résumé des corrections
    show_fix_summary()
    
    # Résumé final
    print("\n" + "=" * 50)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHOUÉ"
        print(f"{test_name:.<30} {status}")
    
    print("-" * 50)
    print(f"Total: {passed}/{total} tests réussis")
    
    if passed == total:
        print("\n🎉 CORRECTION VALIDÉE!")
        print("✅ Le problème data:, devrait être résolu")
        print("🚀 Testez maintenant avec python main.py")
    else:
        print("\n⚠️  Problèmes détectés")
        print("🔧 Vérifiez les erreurs ci-dessus")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
