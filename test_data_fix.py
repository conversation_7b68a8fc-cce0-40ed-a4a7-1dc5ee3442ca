#!/usr/bin/env python3
"""
Test pour vérifier que le problème "data:," est résolu
"""

import time
from blsFacilateWork import BlsFacilateWork

def test_data_fix():
    """
    Test pour vérifier que le navigateur navigue vers la vraie URL BLS
    """
    print("=== Test de correction du problème data:, ===")
    
    try:
        # Créer une instance de BlsFacilateWork
        bls = BlsFacilateWork()
        
        # Configuration de test
        bls.all_manual = True  # Mode manuel pour voir ce qui se passe
        bls.fast_mode = False
        
        # Données de test simples
        test_user_data = [
            "test_user_id",
            "<EMAIL>",
            "test_password"
        ]
        
        # Ajouter les données de test
        bls.data = {0: test_user_data}
        bls.Cookies = {0: None}
        
        print("Configuration terminée. Test de navigation...")
        
        # Tester la session avec auto=False pour mode manuel
        print("Démarrage de la session de test...")
        bls.make_session(0, auto=False)
        
        print("✅ Test terminé avec succès!")
        print("Le navigateur devrait maintenant afficher la page BLS au lieu de 'data:,'")
        
    except Exception as e:
        print(f"❌ Erreur pendant le test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_data_fix()
