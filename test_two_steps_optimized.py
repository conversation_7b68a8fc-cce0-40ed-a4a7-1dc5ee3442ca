#!/usr/bin/env python3
"""
Test des 2 étapes optimisées : Email puis Mot de passe + Captcha
"""

import time
from blsFacilateWork import BlsFacilateWork

def test_two_steps_optimized():
    """
    Test des 2 étapes optimisées du processus de connexion BLS
    """
    print("=== TEST 2 ÉTAPES OPTIMISÉES ===")
    
    try:
        print("🎯 OBJECTIF: Optimiser les 2 étapes de connexion BLS")
        print("📋 PROCESSUS BLS:")
        print("   📧 ÉTAPE 1: Email → Verify → Redirection")
        print("   🔑 ÉTAPE 2: Mot de passe + Captcha → Connexion")
        
        # Créer une instance
        bls = BlsFacilateWork()
        
        # Données du compte
        test_email = "<EMAIL>"
        test_password = "Karim1978"
        test_data = [
            test_email,        # Index 0: Email
            test_password,     # Index 1: Password  
            "ALGER",          # Index 2: Place
            "3",              # Index 3: Member
            "",               # Index 4: N carte
            "",               # Index 5: CVV
            "",               # Index 6: Nom Carte
            ""                # Index 7: Expiry
        ]
        
        bls.data = {test_email: test_data}
        bls.Cookies = {test_email: None}
        
        print(f"✅ Compte configuré: {test_email}")
        print("🚀 Démarrage du test 2 ÉTAPES...")
        print("📋 Optimisations 2 étapes:")
        print("   🔧 Chrome démarre directement sur page 1 (email)")
        print("   ⚡ ÉTAPE 1: Email injecté + Verify cliqué (300ms)")
        print("   🔄 Attente redirection vers page 2 (3s)")
        print("   ⚡ ÉTAPE 2: Mot de passe injecté (200ms retry)")
        print("   🎯 Captcha détecté pour résolution manuelle")
        
        # Démarrer le processus
        start_time = time.perf_counter()
        
        print(f"\n⏱️  Démarrage à {time.strftime('%H:%M:%S')}")
        print("🔍 ÉTAPES ATTENDUES (2 ÉTAPES OPTIMISÉES):")
        print("   1. 🚀 Chrome s'ouvre sur page 1 (email) (0-2s)")
        print("   2. 📧 ÉTAPE 1: Email injecté + Verify cliqué (2-5s)")
        print("   3. 🔄 Redirection vers page 2 (5-8s)")
        print("   4. 🔑 ÉTAPE 2: Mot de passe injecté (8-10s)")
        print("   5. 🎯 Captcha détecté (10-12s)")
        print("   6. ✅ TOTAL: 12-15 secondes + captcha manuel")
        
        # Démarrer le processus
        bls.make_session(test_email, False)
        
        end_time = time.perf_counter()
        duration = end_time - start_time
        
        print(f"\n⏱️  Fin à {time.strftime('%H:%M:%S')}")
        print(f"⚡ Test 2 ÉTAPES terminé en {duration:.2f} secondes!")
        
        # Analyse des 2 étapes
        if duration < 15:
            print("🏆 SUCCÈS EXCEPTIONNEL - 2 ÉTAPES ULTRA-RAPIDES ! (< 15s)")
            print("   🎉 Vitesse humaine atteinte !")
        elif duration < 20:
            print("🏆 SUCCÈS EXCELLENT - 2 étapes rapides (< 20s)")
            print("   ✅ Très bonne performance")
        elif duration < 30:
            print("✅ SUCCÈS BON - 2 étapes acceptables (< 30s)")
            print("   👍 Performance correcte")
        elif duration < 45:
            print("⚠️  PERFORMANCE MOYENNE - Peut mieux faire (< 45s)")
            print("   🔧 Optimisations supplémentaires possibles")
        else:
            print("❌ ÉCHEC - ENCORE TROP LENT (> 45s)")
            print("   🔥 Problème dans les 2 étapes !")
        
        print("\n📊 ANALYSE DES 2 ÉTAPES:")
        print(f"   ⏱️  Durée totale: {duration:.2f} secondes")
        print("   📧 ÉTAPE 1 (Email): ~5 secondes attendues")
        print("   🔑 ÉTAPE 2 (Password): ~5 secondes attendues")
        print("   🎯 Captcha: Résolution manuelle")
        
        print("\n🎯 DIAGNOSTIC 2 ÉTAPES:")
        if duration < 25:
            print("   ✅ Les 2 étapes sont optimisées")
            print("   ✅ Redirection rapide entre étapes")
            print("   ✅ Injection efficace sur chaque page")
        else:
            print("   ❌ Lenteur dans une des 2 étapes")
            print("   🔧 Vérifier les délais de redirection")
            print("   🔧 Optimiser la détection des champs")
        
        print("\n💡 PROCHAINES OPTIMISATIONS:")
        print("   🔧 Réduire délai de redirection (3s → 1s)")
        print("   🔧 Détection plus rapide champ mot de passe")
        print("   🔧 Résolution automatique du captcha")
        print("   🔧 Mode headless pour plus de vitesse")
        
        print("\n🎯 QUESTIONS POUR VALIDATION:")
        print("   ❓ L'email a-t-il été saisi sur la page 1 ?")
        print("   ❓ Y a-t-il eu redirection vers une page 2 ?")
        print("   ❓ Le mot de passe a-t-il été saisi sur la page 2 ?")
        print("   ❓ Le captcha est-il apparu sur la page 2 ?")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

def show_two_steps_process():
    """
    Explique le processus 2 étapes de BLS
    """
    print("=== PROCESSUS 2 ÉTAPES BLS ===")
    
    print("\n📧 ÉTAPE 1 - PAGE EMAIL:")
    print("   🌐 URL: .../Account/LogIn?ReturnUrl=...")
    print("   📝 Champ: Email (input type='text')")
    print("   🔘 Bouton: 'Verify' ou 'Submit'")
    print("   ➡️  Action: Redirection vers page 2")
    
    print("\n🔑 ÉTAPE 2 - PAGE MOT DE PASSE + CAPTCHA:")
    print("   🌐 URL: Nouvelle page après redirection")
    print("   📝 Champ: Mot de passe (input type='password')")
    print("   🎯 Captcha: Grille de nombres à sélectionner")
    print("   🔘 Bouton: 'Login' ou 'Submit'")
    print("   ➡️  Action: Connexion réussie")
    
    print("\n⚡ OPTIMISATIONS APPLIQUÉES:")
    print("   🚀 Chrome démarre directement sur page 1")
    print("   📧 Email injecté en 300ms")
    print("   🔘 Verify cliqué en 100ms")
    print("   🔄 Attente redirection optimisée (3s)")
    print("   🔑 Mot de passe injecté dès détection (200ms retry)")
    print("   🎯 Captcha détecté pour résolution manuelle")
    
    print("\n🎯 OBJECTIF FINAL:")
    print("   ⚡ ÉTAPE 1: 5 secondes max")
    print("   ⚡ ÉTAPE 2: 5 secondes max")
    print("   🎯 Captcha: Résolution manuelle")
    print("   ✅ TOTAL: 10-15 secondes + captcha")

if __name__ == "__main__":
    show_two_steps_process()
    print("\n" + "="*50 + "\n")
    test_two_steps_optimized()
