#!/usr/bin/env python3
"""
Test du nouveau système de captcha automatique
"""

import time
from blsFacilateWork import BlsFacilateWork

def test_captcha_system():
    """
    Test du nouveau système de résolution de captcha
    """
    print("=== TEST DU NOUVEAU SYSTÈME DE CAPTCHA ===")
    
    try:
        # Créer une instance
        bls = BlsFacilateWork()
        
        print(f"✅ Configuration captcha:")
        print(f"   🤖 auto_captcha_enabled: {bls.auto_captcha_enabled}")
        print(f"   🔧 use_new_login_system: {bls.use_new_login_system}")
        
        # Données du vrai compte
        test_email = "<EMAIL>"
        test_password = "Karim1978"
        test_data = [
            test_email,        # Index 0: Email
            test_password,     # Index 1: Password  
            "ALGER",          # Index 2: Place
            "3",              # Index 3: Member
            "",               # Index 4: N carte
            "",               # Index 5: CVV
            "",               # Index 6: Nom Carte
            ""                # Index 7: Expiry
        ]
        
        # Ajouter aux structures
        bls.data = {test_email: test_data}
        bls.Cookies = {test_email: None}
        
        print(f"✅ Compte configuré: {test_email}")
        print("🚀 Démarrage du processus avec nouveau captcha...")
        print("📋 Nouveau système de captcha:")
        print("   - Détection automatique de la consigne")
        print("   - Extraction du nombre cible")
        print("   - Clic automatique sur les bonnes cases")
        print("   - Soumission automatique")
        
        # Démarrer le processus
        start_time = time.time()
        bls.make_session(test_email, False)
        end_time = time.time()
        
        duration = end_time - start_time
        print(f"✅ Processus terminé en {duration:.2f} secondes!")
        
        print("📋 Le nouveau système de captcha devrait:")
        print("   1. ✅ Détecter la consigne 'Please select all boxes with number X'")
        print("   2. ✅ Extraire le nombre cible (ex: 106)")
        print("   3. ✅ Trouver toutes les cases contenant ce nombre")
        print("   4. ✅ Cliquer automatiquement sur les bonnes cases")
        print("   5. ✅ Soumettre le captcha automatiquement")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_captcha_system()
