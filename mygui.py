# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'mygui.ui'
#
# Created by: PyQt5 UI code generator 5.15.10
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON>ore, QtGui, QtWidgets


class Ui_Dialog(object):
    def setupUi(self, Dialog):
        Dialog.setObjectName("Dialog")
        Dialog.setEnabled(True)
        Dialog.resize(1200, 700)  # Fenêtre agrandie pour nouvelles fonctionnalités
        Dialog.setWindowTitle("BLS Spain Visa Bot - Enhanced 2025")

        # Bouton principal Start
        self.startButton = QtWidgets.QPushButton(Dialog)
        self.startButton.setGeometry(QtCore.QRect(300, 30, 211, 41))
        self.startButton.setObjectName("startButton")

        # CheckBoxes existantes
        self.manualCheckBox = QtWidgets.QCheckBox(Dialog)
        self.manualCheckBox.setEnabled(True)
        self.manualCheckBox.setGeometry(QtCore.QRect(950, 70, 41, 25))
        self.manualCheckBox.setAutoFillBackground(False)
        self.manualCheckBox.setText("")
        self.manualCheckBox.setChecked(False)  # Non coché par défaut pour mode automatique
        self.manualCheckBox.setObjectName("manualCheckBox")
        self.manualCheckBox.setToolTip("Mode manuel - Intervention manuelle requise")

        self.loginAllCheckBox = QtWidgets.QCheckBox(Dialog)
        self.loginAllCheckBox.setGeometry(QtCore.QRect(950, 40, 31, 25))
        self.loginAllCheckBox.setText("")
        self.loginAllCheckBox.setChecked(False)
        self.loginAllCheckBox.setObjectName("loginAllCheckBox")
        self.loginAllCheckBox.setToolTip("Connecter tous les comptes automatiquement")

        # Nouvelles CheckBoxes pour les fonctionnalités 2025
        self.autoCaptchaCheckBox = QtWidgets.QCheckBox(Dialog)
        self.autoCaptchaCheckBox.setGeometry(QtCore.QRect(950, 100, 31, 25))
        self.autoCaptchaCheckBox.setText("")
        self.autoCaptchaCheckBox.setChecked(True)
        self.autoCaptchaCheckBox.setObjectName("autoCaptchaCheckBox")
        self.autoCaptchaCheckBox.setToolTip("Résolution automatique des captchas")

        self.fastModeCheckBox = QtWidgets.QCheckBox(Dialog)
        self.fastModeCheckBox.setGeometry(QtCore.QRect(950, 130, 31, 25))
        self.fastModeCheckBox.setText("")
        self.fastModeCheckBox.setChecked(False)
        self.fastModeCheckBox.setObjectName("fastModeCheckBox")
        self.fastModeCheckBox.setToolTip("Mode rapide - Utilise les cookies existants")

        # Labels pour les contrôles
        self.label = QtWidgets.QLabel(Dialog)
        self.label.setGeometry(QtCore.QRect(880, 40, 71, 21))
        self.label.setObjectName("label")

        self.label_2 = QtWidgets.QLabel(Dialog)
        self.label_2.setGeometry(QtCore.QRect(880, 70, 61, 20))
        self.label_2.setObjectName("label_2")

        self.label_3 = QtWidgets.QLabel(Dialog)
        self.label_3.setGeometry(QtCore.QRect(880, 100, 71, 20))
        self.label_3.setObjectName("label_3")
        self.label_3.setText("Auto Captcha")

        self.label_4 = QtWidgets.QLabel(Dialog)
        self.label_4.setGeometry(QtCore.QRect(880, 130, 71, 20))
        self.label_4.setObjectName("label_4")
        self.label_4.setText("Fast Mode")

        # Nouveau ComboBox pour les sous-types de visa
        self.visaSubTypeComboBox = QtWidgets.QComboBox(Dialog)
        self.visaSubTypeComboBox.setGeometry(QtCore.QRect(600, 40, 100, 25))
        self.visaSubTypeComboBox.setObjectName("visaSubTypeComboBox")
        self.visaSubTypeComboBox.addItems(["ALG1", "ALG2", "ALG3", "ALG4"])
        self.visaSubTypeComboBox.setToolTip("Type de demande: ALG1=Première demande, ALG2=Renouvellement, ALG3=Regroupement familial, ALG4=Affaires")

        self.label_5 = QtWidgets.QLabel(Dialog)
        self.label_5.setGeometry(QtCore.QRect(520, 40, 80, 20))
        self.label_5.setObjectName("label_5")
        self.label_5.setText("Visa SubType:")

        # SpinBox pour timeout
        self.timeoutSpinBox = QtWidgets.QSpinBox(Dialog)
        self.timeoutSpinBox.setGeometry(QtCore.QRect(600, 70, 100, 25))
        self.timeoutSpinBox.setObjectName("timeoutSpinBox")
        self.timeoutSpinBox.setMinimum(5)
        self.timeoutSpinBox.setMaximum(300)
        self.timeoutSpinBox.setValue(30)
        self.timeoutSpinBox.setSuffix(" sec")
        self.timeoutSpinBox.setToolTip("Timeout réseau en secondes (5-300)")

        self.label_6 = QtWidgets.QLabel(Dialog)
        self.label_6.setGeometry(QtCore.QRect(520, 70, 80, 20))
        self.label_6.setObjectName("label_6")
        self.label_6.setText("Timeout:")

        # Table principale avec colonne Password ajoutée
        self.tableWidget = QtWidgets.QTableWidget(Dialog)
        self.tableWidget.setGeometry(QtCore.QRect(10, 160, 1180, 400))
        self.tableWidget.setObjectName("tableWidget")
        self.tableWidget.setColumnCount(0)
        self.tableWidget.setRowCount(0)

        # Table d'ajout avec colonne Password
        self.addTable = QtWidgets.QTableWidget(Dialog)
        self.addTable.setGeometry(QtCore.QRect(10, 580, 900, 51))
        self.addTable.setObjectName("addTable")
        self.addTable.setColumnCount(0)
        self.addTable.setRowCount(0)

        self.addButton = QtWidgets.QPushButton(Dialog)
        self.addButton.setGeometry(QtCore.QRect(920, 600, 88, 27))
        self.addButton.setObjectName("addButton")
        self.addButton.setToolTip("Ajouter un nouveau compte")

        # Nouveaux boutons pour Export/Import
        self.exportButton = QtWidgets.QPushButton(Dialog)
        self.exportButton.setGeometry(QtCore.QRect(1020, 600, 80, 27))
        self.exportButton.setObjectName("exportButton")
        self.exportButton.setText("Export")
        self.exportButton.setToolTip("Exporter la configuration")

        self.importButton = QtWidgets.QPushButton(Dialog)
        self.importButton.setGeometry(QtCore.QRect(1110, 600, 80, 27))
        self.importButton.setObjectName("importButton")
        self.importButton.setText("Import")
        self.importButton.setToolTip("Importer une configuration")

        # Label de statut en temps réel
        self.statusLabel = QtWidgets.QLabel(Dialog)
        self.statusLabel.setGeometry(QtCore.QRect(10, 640, 800, 25))
        self.statusLabel.setObjectName("statusLabel")
        self.statusLabel.setText("Prêt - En attente de démarrage...")
        self.statusLabel.setStyleSheet("QLabel { color: #2E8B57; font-weight: bold; }")

        # Barre de progression
        self.progressBar = QtWidgets.QProgressBar(Dialog)
        self.progressBar.setGeometry(QtCore.QRect(10, 670, 1180, 20))
        self.progressBar.setObjectName("progressBar")
        self.progressBar.setValue(0)
        self.progressBar.setVisible(False)  # Cachée par défaut

        # Configuration des tables - retranslateUi sera appelé à la fin

        # Configuration des colonnes pour la table principale
        self.tableWidget.setColumnCount(9)
        self.tableWidget.setHorizontalHeaderLabels([
            'Email', 'Password', 'Place', 'Member', 'N carte', 'CVV', 'Nom Carte', 'Expiry', 'Open/Close'
        ])

        # Configuration des colonnes pour la table d'ajout
        self.addTable.setColumnCount(8)
        self.addTable.setHorizontalHeaderLabels([
            'Email', 'Password', 'Place', 'Member', 'N carte', 'CVV', 'Nom Carte', 'Expiry'
        ])

        # Redimensionnement automatique des colonnes
        self.tableWidget.horizontalHeader().setStretchLastSection(True)
        self.addTable.horizontalHeader().setStretchLastSection(True)

        # Ajustement automatique de la largeur des colonnes
        for i in range(8):
            self.tableWidget.horizontalHeader().setSectionResizeMode(i, QtWidgets.QHeaderView.ResizeToContents)
            if i < 8:
                self.addTable.horizontalHeader().setSectionResizeMode(i, QtWidgets.QHeaderView.ResizeToContents)

        # Configuration spéciale pour la dernière colonne (boutons)
        self.tableWidget.horizontalHeader().setSectionResizeMode(8, QtWidgets.QHeaderView.Fixed)
        self.tableWidget.setColumnWidth(8, 100)

        # Boutons existants repositionnés
        self.deleteButton = QtWidgets.QPushButton(Dialog)
        self.deleteButton.setGeometry(QtCore.QRect(1020, 570, 88, 27))
        self.deleteButton.setObjectName("deleteButton")
        self.deleteButton.setToolTip("Supprimer le compte sélectionné")

        self.saveButton = QtWidgets.QPushButton(Dialog)
        self.saveButton.setGeometry(QtCore.QRect(1110, 570, 88, 27))
        self.saveButton.setObjectName("saveButton")
        self.saveButton.setToolTip("Sauvegarder les modifications")

        # ComboBox pour calendrier repositionné
        self.calenderBox = QtWidgets.QComboBox(Dialog)
        self.calenderBox.setGeometry(QtCore.QRect(750, 40, 100, 25))
        self.calenderBox.setObjectName("calenderBox")
        self.calenderBox.setToolTip("Sélectionner le calendrier de disponibilité")

        self.label_calendar = QtWidgets.QLabel(Dialog)
        self.label_calendar.setGeometry(QtCore.QRect(750, 20, 67, 19))
        self.label_calendar.setObjectName("label_calendar")
        self.label_calendar.setText("Calendar:")

        # CheckBoxes existantes repositionnées
        self.selfieCheckBox = QtWidgets.QCheckBox(Dialog)
        self.selfieCheckBox.setGeometry(QtCore.QRect(20, 100, 120, 25))
        self.selfieCheckBox.setChecked(True)
        self.selfieCheckBox.setObjectName("selfieCheckBox")
        self.selfieCheckBox.setToolTip("Prise de selfie automatique")

        self.paymentCheckBox = QtWidgets.QCheckBox(Dialog)
        self.paymentCheckBox.setGeometry(QtCore.QRect(20, 130, 120, 25))
        self.paymentCheckBox.setChecked(True)
        self.paymentCheckBox.setObjectName("paymentCheckBox")
        self.paymentCheckBox.setToolTip("Paiement automatique")

        # Appel final de retranslateUi et connectSlotsByName
        self.retranslateUi(Dialog)
        QtCore.QMetaObject.connectSlotsByName(Dialog)

    def retranslateUi(self, Dialog):
        _translate = QtCore.QCoreApplication.translate
        Dialog.setWindowTitle(_translate("Dialog", "BLS Spain Visa Bot - Enhanced 2025"))
        self.startButton.setText(_translate("Dialog", "Start"))
        self.label.setText(_translate("Dialog", "Login All"))
        self.label_2.setText(_translate("Dialog", "Manual"))
        self.addButton.setText(_translate("Dialog", "Add"))
        self.deleteButton.setText(_translate("Dialog", "Delete"))
        self.saveButton.setText(_translate("Dialog", "Save"))
        self.selfieCheckBox.setText(_translate("Dialog", "Selfie Auto"))
        self.paymentCheckBox.setText(_translate("Dialog", "Payment Auto"))
