#!/usr/bin/env python3
"""
Test du flux de connexion avec les vraies données du profil
"""

import time
from blsFacilateWork import BlsFacilateWork

def test_login_flow():
    """
    Test du flux de connexion avec email et mot de passe du profil
    """
    print("=== TEST DU FLUX DE CONNEXION ===")
    
    try:
        # Créer une instance
        bls = BlsFacilateWork()
        
        print(f"✅ all_manual par défaut: {bls.all_manual}")
        print(f"✅ fast_mode par défaut: {bls.fast_mode}")
        
        # Données de test avec un vrai compte existant (depuis data.json)
        test_email = "<EMAIL>"
        test_password = "Karim1978"  # Vrai mot de passe du compte
        test_data = [
            test_email,        # Index 0: Email
            test_password,     # Index 1: Password
            "ALGER",          # Index 2: Place
            "3",              # Index 3: Member
            "",               # Index 4: N carte
            "",               # Index 5: CVV
            "",               # Index 6: Nom Carte
            ""                # Index 7: Expiry
        ]
        
        # Ajouter aux structures
        bls.data = {test_email: test_data}
        bls.Cookies = {test_email: None}
        
        print(f"✅ Données configurées:")
        print(f"   📧 Email: {test_data[0]}")
        print(f"   🔒 Password: {test_data[1]}")
        print(f"   📍 Place: {test_data[2]}")
        
        print("🚀 Démarrage du processus avec connexion automatique...")
        
        # Appel comme dans main.py
        bls.make_session(test_email, False)
        
        print("✅ Processus de connexion terminé!")
        print("📋 Le navigateur devrait avoir:")
        print("   1. Navigué vers la page BLS")
        print("   2. Cliqué sur les boutons de réservation")
        print("   3. Rempli automatiquement l'email et le mot de passe")
        print("   4. Cliqué sur le bouton de connexion")
        print("   5. Continué vers le processus de réservation")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_login_flow()
