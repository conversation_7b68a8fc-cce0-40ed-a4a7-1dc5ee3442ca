# -*- coding: utf-8 -*-
"""
Test final pour vérifier que toutes les améliorations BLS 2025 fonctionnent
"""

import sys
import os
import json

def test_complete_system():
    """Test complet du système"""
    print("🚀 TEST COMPLET DU SYSTÈME BLS 2025")
    print("=" * 50)
    
    results = []
    
    # Test 1: Vérification des fichiers
    print("\n📁 Test 1: Vérification des fichiers...")
    required_files = ['data.json', 'mygui.py', 'blsFacilateWork.py', 'main.py']
    files_ok = True
    for file in required_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} manquant")
            files_ok = False
    results.append(("Fichiers requis", files_ok))
    
    # Test 2: Structure de données
    print("\n📊 Test 2: Structure de données...")
    try:
        with open('data.json', 'r') as f:
            data = json.load(f)
        
        data_ok = True
        for email, user_data in data.items():
            if len(user_data) < 8:
                print(f"  ❌ {email}: Structure incomplète ({len(user_data)} éléments)")
                data_ok = False
            else:
                print(f"  ✅ {email}: Structure correcte ({len(user_data)} éléments)")
        
        results.append(("Structure de données", data_ok))
        
    except Exception as e:
        print(f"  ❌ Erreur: {e}")
        results.append(("Structure de données", False))
    
    # Test 3: Interface utilisateur
    print("\n🖥️  Test 3: Interface utilisateur...")
    try:
        from PyQt5.QtWidgets import QApplication, QDialog
        from mygui import Ui_Dialog
        
        # Créer une application Qt minimale
        if not QApplication.instance():
            app = QApplication([])
        
        dialog = QDialog()
        ui = Ui_Dialog()
        ui.setupUi(dialog)
        
        # Vérifier les nouveaux éléments
        new_elements = [
            'autoCaptchaCheckBox', 'fastModeCheckBox', 'visaSubTypeComboBox',
            'timeoutSpinBox', 'statusLabel', 'progressBar', 'exportButton', 'importButton'
        ]
        
        ui_ok = True
        for element in new_elements:
            if hasattr(ui, element):
                print(f"  ✅ {element}")
            else:
                print(f"  ❌ {element} manquant")
                ui_ok = False
        
        # Vérifier les tables
        if ui.tableWidget.columnCount() == 9:
            print("  ✅ Table principale: 9 colonnes (avec Password)")
        else:
            print(f"  ❌ Table principale: {ui.tableWidget.columnCount()} colonnes")
            ui_ok = False
            
        if ui.addTable.columnCount() == 8:
            print("  ✅ Table d'ajout: 8 colonnes (avec Password)")
        else:
            print(f"  ❌ Table d'ajout: {ui.addTable.columnCount()} colonnes")
            ui_ok = False
        
        results.append(("Interface utilisateur", ui_ok))
        
    except Exception as e:
        print(f"  ❌ Erreur: {e}")
        results.append(("Interface utilisateur", False))
    
    # Test 4: Fonctions BLS
    print("\n🔧 Test 4: Nouvelles fonctions BLS...")
    try:
        import blsFacilateWork
        
        bls_class = blsFacilateWork.BlsFacilateWork
        new_methods = [
            'validate_application_type',
            'check_time_slots_availability',
            'select_visa_subtype',
            'retry_with_backoff',
            'detect_site_changes'
        ]
        
        bls_ok = True
        for method in new_methods:
            if hasattr(bls_class, method):
                print(f"  ✅ {method}")
            else:
                print(f"  ❌ {method} manquant")
                bls_ok = False
        
        results.append(("Nouvelles fonctions BLS", bls_ok))
        
    except Exception as e:
        print(f"  ❌ Erreur: {e}")
        results.append(("Nouvelles fonctions BLS", False))
    
    # Test 5: Correction du mot de passe
    print("\n🔐 Test 5: Correction du mot de passe...")
    try:
        with open('blsFacilateWork.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'self.data[session_number][1].encode' in content:
            print("  ✅ Mot de passe utilise les données JSON")
            password_ok = True
        else:
            print("  ❌ Mot de passe encore codé en dur")
            password_ok = False
        
        results.append(("Correction mot de passe", password_ok))
        
    except Exception as e:
        print(f"  ❌ Erreur: {e}")
        results.append(("Correction mot de passe", False))
    
    # Test 6: Import de l'application principale
    print("\n🎯 Test 6: Application principale...")
    try:
        import main
        
        if hasattr(main, 'MyGUI'):
            print("  ✅ Classe MyGUI trouvée")
            main_ok = True
        else:
            print("  ❌ Classe MyGUI non trouvée")
            main_ok = False
        
        results.append(("Application principale", main_ok))
        
    except Exception as e:
        print(f"  ❌ Erreur: {e}")
        results.append(("Application principale", False))
    
    # Résumé final
    print("\n" + "=" * 50)
    print("📊 RÉSUMÉ FINAL")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHOUÉ"
        print(f"{test_name:.<30} {status}")
    
    print("-" * 50)
    print(f"Total: {passed}/{total} tests réussis")
    
    if passed == total:
        print("\n🎉 FÉLICITATIONS!")
        print("✅ Toutes les améliorations BLS 2025 sont opérationnelles")
        print("🚀 Votre bot est prêt à utiliser avec les nouvelles fonctionnalités:")
        print("   • Gestion des mots de passe individuels")
        print("   • Support des sous-types de visa ALG1-ALG4")
        print("   • Vérification automatique des créneaux 20h")
        print("   • Interface améliorée avec nouvelles fonctionnalités")
        print("   • Gestion d'erreurs robuste avec retry automatique")
        print("   • Surveillance des changements de site")
        print("\n🔧 Pour utiliser le bot:")
        print("   python main.py")
        
    else:
        print("\n⚠️  ATTENTION!")
        print(f"❌ {total - passed} test(s) ont échoué")
        print("🔧 Vérifiez les erreurs ci-dessus avant d'utiliser le bot")
    
    return passed == total

def show_new_features():
    """Affiche les nouvelles fonctionnalités"""
    print("\n🆕 NOUVELLES FONCTIONNALITÉS 2025")
    print("=" * 50)
    
    features = [
        ("🔐 Mots de passe individuels", "Chaque compte a son propre mot de passe"),
        ("🎯 Sous-types de visa", "Support ALG1, ALG2, ALG3, ALG4"),
        ("⏰ Créneaux 20h", "Attente automatique jusqu'à 20h"),
        ("🖥️  Interface améliorée", "Nouveaux contrôles et fonctionnalités"),
        ("🔄 Retry automatique", "Mécanisme de retry avec backoff exponentiel"),
        ("🔍 Surveillance site", "Détection automatique des changements"),
        ("📊 Logging avancé", "Logs détaillés pour le débogage"),
        ("💾 Export/Import", "Sauvegarde et chargement des configurations")
    ]
    
    for feature, description in features:
        print(f"{feature:.<25} {description}")

if __name__ == "__main__":
    success = test_complete_system()
    
    if success:
        show_new_features()
    
    sys.exit(0 if success else 1)
