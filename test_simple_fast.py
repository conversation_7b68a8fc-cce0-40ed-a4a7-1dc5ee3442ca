#!/usr/bin/env python3
"""
Test simple et rapide pour diagnostiquer
"""

import time
from blsFacilateWork import BlsFacilateWork

def test_simple_fast():
    """
    Test simple pour voir ce qui se passe
    """
    print("=== TEST SIMPLE RAPIDE ===")
    
    try:
        print("1. Création de l'instance BLS...")
        bls = BlsFacilateWork()
        print("✅ Instance créée")
        
        # Données du compte
        test_email = "<EMAIL>"
        test_password = "Karim1978"
        test_data = [
            test_email,        # Index 0: Email
            test_password,     # Index 1: Password  
            "ALGER",          # Index 2: Place
            "3",              # Index 3: Member
            "",               # Index 4: N carte
            "",               # Index 5: CVV
            "",               # Index 6: Nom Carte
            ""                # Index 7: Expiry
        ]
        
        print("2. Configuration des données...")
        bls.data = {test_email: test_data}
        bls.Cookies = {test_email: None}
        print("✅ Données configurées")
        
        print("3. Démarrage du processus...")
        start_time = time.perf_counter()
        
        # Démarrer le processus
        print("🚀 Appel de make_session...")
        bls.make_session(test_email, False)
        
        end_time = time.perf_counter()
        duration = end_time - start_time
        
        print(f"✅ Processus terminé en {duration:.2f} secondes")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple_fast()
