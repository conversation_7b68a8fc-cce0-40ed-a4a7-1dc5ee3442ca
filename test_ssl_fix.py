#!/usr/bin/env python3
"""
Test pour vérifier que le problème SSL "Non sécurisé" est résolu
"""

import time
from blsFacilateWork import BlsFacilateWork

def test_ssl_fix():
    """
    Test pour vérifier que le navigateur n'affiche plus "Non sécurisé"
    """
    print("=== TEST CORRECTION SSL ===")
    
    try:
        # Créer une instance
        bls = BlsFacilateWork()
        
        print(f"🔒 Configuration SSL:")
        print(f"   ✅ Options Chrome SSL ajoutées")
        print(f"   ✅ Seleniumwire SSL configuré")
        print(f"   ✅ Certificats ignorés")
        print(f"   ✅ Sécurité web désactivée")
        
        # Données du vrai compte
        test_email = "<EMAIL>"
        test_password = "Karim1978"
        test_data = [
            test_email,        # Index 0: Email
            test_password,     # Index 1: Password  
            "ALGER",          # Index 2: Place
            "3",              # Index 3: Member
            "",               # Index 4: N carte
            "",               # Index 5: CVV
            "",               # Index 6: Nom Carte
            ""                # Index 7: Expiry
        ]
        
        # Ajouter aux structures
        bls.data = {test_email: test_data}
        bls.Cookies = {test_email: None}
        
        print(f"✅ Compte configuré: {test_email}")
        print("🔒 Démarrage du test SSL...")
        print("📋 Corrections SSL appliquées:")
        print("   - --ignore-certificate-errors")
        print("   - --ignore-ssl-errors")
        print("   - --ignore-certificate-errors-spki-list")
        print("   - --disable-web-security")
        print("   - --allow-running-insecure-content")
        print("   - --reduce-security-for-testing")
        print("   - --test-type")
        print("   - verify_ssl: False dans seleniumwire")
        print("   - suppress_connection_errors: True")
        
        # Démarrer le processus
        start_time = time.perf_counter()
        
        print(f"\n⏱️  Démarrage à {time.strftime('%H:%M:%S')}")
        print("🔍 Surveillez la barre d'adresse du navigateur...")
        print("   ❌ AVANT: 'Non sécurisé' en rouge avec HTTPS barré")
        print("   ✅ APRÈS: Devrait être normal ou avec un cadenas")
        
        # Démarrer le processus
        bls.make_session(test_email, False)
        
        end_time = time.perf_counter()
        duration = end_time - start_time
        
        print(f"\n⏱️  Fin à {time.strftime('%H:%M:%S')}")
        print(f"🔒 Test SSL terminé en {duration:.2f} secondes!")
        
        print("\n📋 Vérifications à faire manuellement:")
        print("   1. 🔍 Regardez la barre d'adresse du navigateur")
        print("   2. ✅ Plus de 'Non sécurisé' en rouge")
        print("   3. ✅ Plus de HTTPS barré")
        print("   4. ✅ Navigation normale vers BLS")
        print("   5. ✅ Connexion fonctionne sans avertissement SSL")
        
        print("\n🎯 RÉSULTATS ATTENDUS:")
        print("   ✅ Barre d'adresse normale")
        print("   ✅ Pas d'avertissement de sécurité")
        print("   ✅ HTTPS fonctionne correctement")
        print("   ✅ Site BLS accessible sans problème")
        
        print("\n💡 SI LE PROBLÈME PERSISTE:")
        print("   - Vérifiez que Chrome est à jour")
        print("   - Redémarrez complètement Chrome")
        print("   - Videz le cache de Chrome")
        print("   - Le problème peut venir de seleniumwire lui-même")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

def check_chrome_options():
    """
    Affiche toutes les options Chrome configurées
    """
    print("\n=== OPTIONS CHROME CONFIGURÉES ===")
    
    options_ssl = [
        "--ignore-certificate-errors",
        "--ignore-ssl-errors", 
        "--ignore-certificate-errors-spki-list",
        "--ignore-urlfetcher-cert-requests",
        "--disable-web-security",
        "--allow-running-insecure-content",
        "--reduce-security-for-testing",
        "--test-type",
        "--disable-extensions-file-access-check",
        "--disable-extensions-http-throttling",
        "--disable-infobars",
        "--disable-notifications",
        "--disable-popup-blocking"
    ]
    
    print("🔒 Options SSL/Sécurité:")
    for option in options_ssl:
        print(f"   ✅ {option}")
    
    print("\n🔧 Options Seleniumwire:")
    print("   ✅ verify_ssl: False")
    print("   ✅ suppress_connection_errors: True")
    print("   ✅ ignore_http_methods: []")
    print("   ✅ disable_encoding: True")

if __name__ == "__main__":
    check_chrome_options()
    print("\n" + "="*50 + "\n")
    test_ssl_fix()
