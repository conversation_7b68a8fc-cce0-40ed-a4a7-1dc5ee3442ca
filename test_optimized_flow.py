#!/usr/bin/env python3
"""
Test du flux optimisé avec captcha automatique et mode rapide
"""

import time
from blsFacilateWork import BlsFacilateWork

def test_optimized_flow():
    """
    Test du flux optimisé avec toutes les améliorations
    """
    print("=== TEST DU FLUX OPTIMISÉ ===")
    
    try:
        # Créer une instance
        bls = BlsFacilateWork()
        
        print(f"✅ Configuration optimisée:")
        print(f"   🤖 auto_captcha: {bls.auto_captcha}")
        print(f"   ⚡ fast_mode: {bls.fast_mode}")
        print(f"   🚀 fast_mode_enabled: {bls.fast_mode_enabled}")
        print(f"   📊 auto_captcha_enabled: {bls.auto_captcha_enabled}")
        print(f"   ⏱️  network_timeout: {bls.network_timeout}")
        print(f"   🔄 base_delay: {bls.base_delay}")
        
        # Données du vrai compte
        test_email = "<EMAIL>"
        test_password = "Karim1978"
        test_data = [
            test_email,        # Index 0: Email
            test_password,     # Index 1: Password  
            "ALGER",          # Index 2: Place
            "3",              # Index 3: Member
            "",               # Index 4: N carte
            "",               # Index 5: CVV
            "",               # Index 6: Nom Carte
            ""                # Index 7: Expiry
        ]
        
        # Ajouter aux structures
        bls.data = {test_email: test_data}
        bls.Cookies = {test_email: None}
        
        print(f"✅ Compte configuré: {test_email}")
        print("🚀 Démarrage du processus optimisé...")
        print("📋 Fonctionnalités actives:")
        print("   - Résolution automatique de captcha")
        print("   - Mode rapide avec cookies")
        print("   - Délais réduits")
        print("   - Connexion en deux étapes")
        
        # Démarrer le processus
        start_time = time.time()
        bls.make_session(test_email, False)
        end_time = time.time()
        
        duration = end_time - start_time
        print(f"✅ Processus terminé en {duration:.2f} secondes!")
        
        print("📋 Le navigateur devrait avoir:")
        print("   1. ✅ Navigué rapidement vers la page BLS")
        print("   2. ✅ Utilisé les cookies pour accélérer")
        print("   3. ✅ Connecté automatiquement avec email/mot de passe")
        print("   4. ✅ Résolu automatiquement les captchas")
        print("   5. ✅ Accédé à la page de réservation")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_optimized_flow()
