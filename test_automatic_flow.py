#!/usr/bin/env python3
"""
Test du nouveau flux automatique
"""

import time
from blsFacilateWork import BlsFacilateWork

def test_automatic_flow():
    """
    Test du flux automatique avec navigation vers les liens suivants
    """
    print("=== TEST DU FLUX AUTOMATIQUE ===")
    
    try:
        # Créer une instance
        bls = BlsFacilateWork()
        
        print(f"✅ all_manual par défaut: {bls.all_manual}")
        print(f"✅ fast_mode par défaut: {bls.fast_mode}")
        
        # Données de test
        test_email = "<EMAIL>"
        test_data = ["test_id", "<EMAIL>", "test_password", "place", "member", "carte", "cvv", "nom"]
        
        # Ajouter aux structures
        bls.data = {test_email: test_data}
        bls.Cookies = {test_email: None}
        
        print(f"✅ Données configurées pour {test_email}")
        print("🚀 Démarrage du processus automatique...")
        
        # Appel comme dans main.py
        bls.make_session(test_email, False)
        
        print("✅ Processus automatique terminé!")
        print("📋 Le navigateur devrait avoir:")
        print("   1. Navigué vers https://algeria.blsspainglobal.com/DZA/")
        print("   2. Cherché et cliqué sur les boutons de réservation")
        print("   3. Navigué vers https://algeria.blsspainglobal.com/DZA/appointment/newappointment")
        print("   4. Affiché 'Sécurisé' au lieu de 'Non sécurisé'")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_automatic_flow()
