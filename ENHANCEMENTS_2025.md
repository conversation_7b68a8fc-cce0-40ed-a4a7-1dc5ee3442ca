# Améliorations BLS Spain Visa Bot 2025

## 📋 Résumé des modifications

Ce document détaille les améliorations apportées au système BLS Spain Visa Bot pour s'adapter aux nouvelles règles et exigences de 2025.

## 🔧 Modifications principales

### 1. Structure de données mise à jour

#### Ancienne structure `data.json`:
```json
{
  "email": [
    "email",
    "location", 
    "members",
    "card_number",
    "cvv", 
    "card_name",
    "expiry"
  ]
}
```

#### Nouvelle structure `data.json`:
```json
{
  "email": [
    "email",
    "password",      // NOUVEAU - Position 1
    "location", 
    "members",
    "card_number",
    "cvv", 
    "card_name",
    "expiry",
    "visa_subtype"   // OPTIONNEL - Position 8
  ]
}
```

### 2. Interface utilisateur améliorée (`mygui.py`)

#### Nouvelles fonctionnalités:
- **Colonne Password** : Ajout de la colonne mot de passe dans les tables
- **Auto Captcha CheckBox** : Active la résolution automatique des captchas
- **Fast Mode CheckBox** : Utilise les cookies existants pour accélération
- **Visa SubType ComboBox** : Sélection ALG1/ALG2/ALG3/ALG4
- **Timeout SpinBox** : Configuration du timeout réseau (5-300 sec)
- **Status Label** : Affichage du statut en temps réel
- **Progress Bar** : Barre de progression pour les opérations longues
- **Export/Import Buttons** : Pour sauvegarder/charger les configurations

#### Améliorations visuelles:
- Fenêtre agrandie (1200x700 pixels)
- Tooltips informatifs
- Redimensionnement automatique des colonnes
- Style visuel amélioré

### 3. Nouvelles fonctions dans `blsFacilateWork.py`

#### `validate_application_type(chrome, user)`
- **But** : Valider le type de demande selon les nouvelles règles BLS 2025
- **Emplacement** : Appelée après `login()` et avant `remplir_formulair()`
- **Fonctionnalité** : Vérifie que le type de rendez-vous correspond à la nature de la demande

#### `check_time_slots_availability(chrome)`
- **But** : Vérifier la disponibilité des créneaux à 20h selon les nouvelles règles
- **Règle 2025** : Rendez-vous disponibles quotidiennement à 20h depuis juillet 2025
- **Fonctionnalité** : Attend automatiquement jusqu'à 20h si nécessaire

#### `select_visa_subtype(chrome, user)`
- **But** : Gérer les nouveaux sous-types de visa ALG1, ALG2, ALG3, ALG4
- **Mapping** :
  - ALG1 : Première demande
  - ALG2 : Renouvellement
  - ALG3 : Regroupement familial
  - ALG4 : Affaires

#### `retry_with_backoff(func, max_retries, base_delay)`
- **But** : Mécanisme de retry avec délai exponentiel
- **Fonctionnalité** : Améliore la robustesse face aux erreurs temporaires

#### `detect_site_changes(chrome)`
- **But** : Détecter les changements dans la structure du site
- **Fonctionnalité** : Surveillance automatique et notifications Telegram

### 4. Corrections de bugs

#### Mot de passe codé en dur (ligne 120)
**Avant** :
```python
password = b'type="password" value="{}"'.replace(b'{}',"A2z0@I0z0".encode('utf-8'))
```

**Après** :
```python
password = b'type="password" value="{}"'.replace(b'{}',self.data[session_number][1].encode('utf-8'))
```

#### Migration automatique des données
- Fonction `migrate_data_structure()` pour convertir l'ancienne structure
- Ajout automatique du mot de passe par défaut pour les comptes existants
- Sauvegarde automatique de la nouvelle structure

## 🚀 Nouvelles règles BLS 2025 implémentées

### 1. Créneaux horaires
- ✅ Rendez-vous disponibles quotidiennement à 20h
- ✅ Attente automatique jusqu'à l'heure de disponibilité

### 2. Catégorisation des demandes
- ✅ Basée sur le dernier visa Schengen (depuis novembre 2024)
- ✅ Validation du type de demande avant soumission

### 3. Sous-types de visa
- ✅ Support des nouveaux codes ALG1, ALG2, ALG3, ALG4
- ✅ Sélection automatique basée sur les données utilisateur

## 🧪 Tests et validation

### Fichier de test : `test_bls_enhancements.py`

#### Tests inclus :
1. **test_init_with_new_features** : Vérification de l'initialisation
2. **test_data_structure_migration** : Test de migration des données
3. **test_validate_application_type** : Test de validation du type
4. **test_check_time_slots_availability** : Test des créneaux horaires
5. **test_select_visa_subtype** : Test de sélection du sous-type
6. **test_retry_with_backoff** : Test du mécanisme de retry

#### Exécution des tests :
```bash
python test_bls_enhancements.py
```

## 📦 Installation et mise à jour

### 1. Sauvegarde
```bash
# Sauvegarder les fichiers existants
cp data.json data.json.backup
cp mygui.py mygui.py.backup
cp blsFacilateWork.py blsFacilateWork.py.backup
```

### 2. Mise à jour
- Remplacer `mygui.py` par la nouvelle version
- Remplacer `blsFacilateWork.py` par la nouvelle version
- La migration des données se fait automatiquement au premier lancement

### 3. Vérification
```bash
# Exécuter les tests
python test_bls_enhancements.py

# Vérifier la structure des données
python -c "import json; print(json.load(open('data.json')))"
```

## 🔧 Configuration

### Variables de configuration dans `__init__()`:
```python
self.auto_captcha_enabled = True    # Résolution auto des captchas
self.fast_mode_enabled = False      # Mode rapide avec cookies
self.visa_subtype = "ALG1"          # Sous-type par défaut
self.network_timeout = 30           # Timeout réseau (secondes)
self.retry_count = 3                # Nombre de tentatives
self.base_delay = 1                 # Délai de base pour retry
```

## 📊 Compatibilité

### Rétrocompatibilité
- ✅ Compatible avec les anciens fichiers `data.json`
- ✅ Migration automatique sans perte de données
- ✅ Préservation de toutes les fonctionnalités existantes

### Nouvelles dépendances
- Aucune nouvelle dépendance requise
- Utilise les modules existants (selenium, json, time, datetime)

## 🐛 Résolution de problèmes

### Problèmes courants

#### 1. Erreur de migration des données
```python
# Vérifier la structure
with open('data.json', 'r') as f:
    data = json.load(f)
    for email, user_data in data.items():
        print(f"{email}: {len(user_data)} éléments")
```

#### 2. Interface ne s'affiche pas correctement
- Vérifier que PyQt5 est installé
- Redémarrer l'application
- Vérifier les logs pour les erreurs

#### 3. Nouvelles fonctions ne fonctionnent pas
- Vérifier que `blsFacilateWork.py` a été mis à jour
- Exécuter les tests pour identifier le problème
- Vérifier les logs d'erreur

## 📞 Support

Pour toute question ou problème :
1. Exécuter d'abord les tests : `python test_bls_enhancements.py`
2. Vérifier les logs d'erreur
3. Consulter ce document pour les solutions courantes

## 🎯 Prochaines étapes

### Améliorations futures possibles :
1. Interface graphique pour la configuration des sous-types
2. Historique des tentatives de connexion
3. Notifications push pour les créneaux disponibles
4. Sauvegarde automatique des configurations
5. Mode debug avancé avec logs détaillés

---

**Version** : 2025.1.0  
**Date de mise à jour** : Janvier 2025  
**Compatibilité** : BLS Spain Algeria 2025
