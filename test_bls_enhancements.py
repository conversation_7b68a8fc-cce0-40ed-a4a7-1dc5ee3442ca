# -*- coding: utf-8 -*-
"""
Test script pour vérifier les améliorations BLS 2025
"""

import unittest
import json
import sys
import os
from unittest.mock import Mock, patch, MagicMock

# Ajouter le répertoire parent au path pour importer blsFacilateWork
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from blsFacilateWork import BlsFacilateWork
except ImportError as e:
    print(f"Erreur d'importation: {e}")
    print("Assurez-vous que blsFacilateWork.py est dans le même répertoire")

class TestBLSEnhancements(unittest.TestCase):
    
    def setUp(self):
        """Configuration avant chaque test"""
        # Mock des données de test
        self.test_data = {
            "<EMAIL>": [
                "<EMAIL>",
                "TestPassword123",
                "ALGER",
                "2",
                "1234567890123456",
                "123",
                "TEST USER",
                "12/25",
                "ALG1"
            ]
        }
        
        # Créer un fichier data.json temporaire pour les tests
        with open('data_test.json', 'w') as f:
            json.dump(self.test_data, f)
        
        # Mock des cookies
        self.test_cookies = {}
        with open('cookies_test.json', 'w') as f:
            json.dump(self.test_cookies, f)
    
    def tearDown(self):
        """Nettoyage après chaque test"""
        # Supprimer les fichiers de test
        for file in ['data_test.json', 'cookies_test.json']:
            if os.path.exists(file):
                os.remove(file)
    
    @patch('builtins.open')
    @patch('json.loads')
    def test_init_with_new_features(self, mock_json_loads, mock_open):
        """Test de l'initialisation avec les nouvelles fonctionnalités"""
        mock_json_loads.side_effect = [self.test_data, self.test_cookies]
        
        try:
            bls = BlsFacilateWork()
            
            # Vérifier que les nouvelles variables sont initialisées
            self.assertTrue(hasattr(bls, 'auto_captcha_enabled'))
            self.assertTrue(hasattr(bls, 'fast_mode_enabled'))
            self.assertTrue(hasattr(bls, 'visa_subtype'))
            self.assertTrue(hasattr(bls, 'network_timeout'))
            self.assertTrue(hasattr(bls, 'retry_count'))
            self.assertTrue(hasattr(bls, 'logger'))
            
            # Vérifier les valeurs par défaut
            self.assertTrue(bls.auto_captcha_enabled)
            self.assertFalse(bls.fast_mode_enabled)
            self.assertEqual(bls.visa_subtype, "ALG1")
            self.assertEqual(bls.network_timeout, 30)
            self.assertEqual(bls.retry_count, 3)
            
            print("✅ Test d'initialisation réussi")
            
        except Exception as e:
            print(f"❌ Erreur lors du test d'initialisation: {e}")
            self.fail(f"Initialisation échouée: {e}")
    
    def test_data_structure_migration(self):
        """Test de la migration de la structure de données"""
        # Données avec ancienne structure (sans mot de passe)
        old_data = {
            "<EMAIL>": [
                "<EMAIL>",
                "ALGER", 
                "1",
                "1234567890123456",
                "123",
                "OLD USER",
                "12/25"
            ]
        }
        
        with patch('builtins.open'), patch('json.loads') as mock_loads, patch('json.dump') as mock_dump:
            mock_loads.side_effect = [old_data, {}]
            
            try:
                bls = BlsFacilateWork()
                
                # Vérifier que la migration a ajouté le mot de passe
                migrated_user = bls.data["<EMAIL>"]
                self.assertEqual(len(migrated_user), 8)  # Doit avoir 8 éléments maintenant
                self.assertEqual(migrated_user[1], "A2z0@I0z0")  # Mot de passe par défaut
                
                print("✅ Test de migration de données réussi")
                
            except Exception as e:
                print(f"❌ Erreur lors du test de migration: {e}")
                self.fail(f"Migration échouée: {e}")
    
    def test_validate_application_type(self):
        """Test de la validation du type de demande"""
        with patch('builtins.open'), patch('json.loads') as mock_loads:
            mock_loads.side_effect = [self.test_data, {}]
            
            try:
                bls = BlsFacilateWork()
                
                # Mock du chrome driver
                mock_chrome = Mock()
                mock_element = Mock()
                
                # Mock WebDriverWait et EC
                with patch.object(bls, 'WebDriverWait') as mock_wait, \
                     patch.object(bls, 'EC') as mock_ec:
                    
                    mock_wait.return_value.until.return_value = mock_element
                    
                    result = bls.validate_application_type(mock_chrome, "<EMAIL>")
                    
                    self.assertTrue(result)
                    print("✅ Test de validation du type de demande réussi")
                    
            except Exception as e:
                print(f"❌ Erreur lors du test de validation: {e}")
                self.fail(f"Validation échouée: {e}")
    
    def test_check_time_slots_availability(self):
        """Test de la vérification des créneaux horaires"""
        with patch('builtins.open'), patch('json.loads') as mock_loads:
            mock_loads.side_effect = [self.test_data, {}]
            
            try:
                bls = BlsFacilateWork()
                
                # Mock du chrome driver
                mock_chrome = Mock()
                
                # Mock datetime pour simuler différentes heures
                with patch('datetime.datetime') as mock_datetime:
                    # Simuler qu'il est 19h (avant 20h)
                    mock_now = Mock()
                    mock_now.time.return_value = Mock()
                    mock_now.time.return_value.__lt__ = Mock(return_value=True)
                    mock_datetime.now.return_value = mock_now
                    
                    # Mock time.sleep pour éviter l'attente réelle
                    with patch.object(bls, 'time') as mock_time:
                        mock_time.sleep = Mock()
                        
                        result = bls.check_time_slots_availability(mock_chrome)
                        
                        self.assertTrue(result)
                        print("✅ Test de vérification des créneaux horaires réussi")
                        
            except Exception as e:
                print(f"❌ Erreur lors du test des créneaux: {e}")
                self.fail(f"Vérification des créneaux échouée: {e}")
    
    def test_select_visa_subtype(self):
        """Test de la sélection du sous-type de visa"""
        with patch('builtins.open'), patch('json.loads') as mock_loads:
            mock_loads.side_effect = [self.test_data, {}]
            
            try:
                bls = BlsFacilateWork()
                
                # Mock du chrome driver
                mock_chrome = Mock()
                mock_element = Mock()
                
                # Mock WebDriverWait et EC
                with patch.object(bls, 'WebDriverWait') as mock_wait, \
                     patch.object(bls, 'EC') as mock_ec:
                    
                    mock_wait.return_value.until.return_value = mock_element
                    
                    result = bls.select_visa_subtype(mock_chrome, "<EMAIL>")
                    
                    self.assertTrue(result)
                    mock_element.click.assert_called_once()
                    print("✅ Test de sélection du sous-type de visa réussi")
                    
            except Exception as e:
                print(f"❌ Erreur lors du test de sélection: {e}")
                self.fail(f"Sélection du sous-type échouée: {e}")
    
    def test_retry_with_backoff(self):
        """Test du mécanisme de retry avec backoff exponentiel"""
        with patch('builtins.open'), patch('json.loads') as mock_loads:
            mock_loads.side_effect = [self.test_data, {}]
            
            try:
                bls = BlsFacilateWork()
                
                # Fonction qui échoue 2 fois puis réussit
                call_count = 0
                def failing_function():
                    nonlocal call_count
                    call_count += 1
                    if call_count < 3:
                        from blsFacilateWork import MyCustomError
                        raise MyCustomError("Test error")
                    return "success"
                
                # Mock time.sleep pour éviter l'attente réelle
                with patch.object(bls, 'time') as mock_time:
                    mock_time.sleep = Mock()
                    
                    result = bls.retry_with_backoff(failing_function, max_retries=3, base_delay=0.1)
                    
                    self.assertEqual(result, "success")
                    self.assertEqual(call_count, 3)
                    print("✅ Test du mécanisme de retry réussi")
                    
            except Exception as e:
                print(f"❌ Erreur lors du test de retry: {e}")
                self.fail(f"Mécanisme de retry échoué: {e}")

def run_tests():
    """Exécute tous les tests"""
    print("🚀 Démarrage des tests des améliorations BLS 2025...")
    print("=" * 60)
    
    # Créer une suite de tests
    suite = unittest.TestLoader().loadTestsFromTestCase(TestBLSEnhancements)
    
    # Exécuter les tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("=" * 60)
    if result.wasSuccessful():
        print("🎉 Tous les tests sont passés avec succès!")
        print(f"✅ {result.testsRun} tests exécutés")
    else:
        print("❌ Certains tests ont échoué")
        print(f"🔴 Échecs: {len(result.failures)}")
        print(f"🔴 Erreurs: {len(result.errors)}")
        
        # Afficher les détails des échecs
        for test, traceback in result.failures + result.errors:
            print(f"\n❌ {test}: {traceback}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
