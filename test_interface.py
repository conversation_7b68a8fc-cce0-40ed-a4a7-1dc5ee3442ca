# -*- coding: utf-8 -*-
"""
Test spécifique pour l'interface graphique
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QDialog

def test_interface():
    """Test de l'interface graphique"""
    print("🖥️  Test de l'interface graphique...")
    
    try:
        # Créer une application Qt
        app = QApplication(sys.argv)
        
        # Importer et créer l'interface
        from mygui import Ui_Dialog
        
        # Créer un dialog de test
        dialog = QDialog()
        ui = Ui_Dialog()
        
        # Tester setupUi (c'est là que l'erreur se produisait)
        ui.setupUi(dialog)
        
        print("✅ Interface créée avec succès!")
        
        # Vérifier que tous les éléments sont présents
        required_elements = [
            'startButton', 'manualCheckBox', 'loginAllCheckBox',
            'autoCaptchaCheckBox', 'fastModeCheckBox', 'visaSubTypeComboBox',
            'timeoutSpinBox', 'tableWidget', 'addTable', 'addButton',
            'deleteButton', 'saveButton', 'exportButton', 'importButton',
            'statusLabel', 'progressBar', 'selfieCheckBox', 'paymentCheckBox'
        ]
        
        missing_elements = []
        for element in required_elements:
            if not hasattr(ui, element):
                missing_elements.append(element)
        
        if missing_elements:
            print(f"❌ Éléments manquants: {missing_elements}")
            return False
        else:
            print("✅ Tous les éléments d'interface sont présents")
        
        # Vérifier les nouvelles fonctionnalités
        print("🔍 Vérification des nouvelles fonctionnalités:")
        
        # Vérifier le ComboBox des sous-types
        if hasattr(ui, 'visaSubTypeComboBox'):
            items = [ui.visaSubTypeComboBox.itemText(i) for i in range(ui.visaSubTypeComboBox.count())]
            expected_items = ["ALG1", "ALG2", "ALG3", "ALG4"]
            if all(item in items for item in expected_items):
                print("  ✅ ComboBox sous-types de visa configuré correctement")
            else:
                print(f"  ❌ ComboBox sous-types incorrect. Attendu: {expected_items}, Trouvé: {items}")
        
        # Vérifier le SpinBox timeout
        if hasattr(ui, 'timeoutSpinBox'):
            if ui.timeoutSpinBox.minimum() == 5 and ui.timeoutSpinBox.maximum() == 300:
                print("  ✅ SpinBox timeout configuré correctement (5-300 sec)")
            else:
                print(f"  ❌ SpinBox timeout incorrect. Min: {ui.timeoutSpinBox.minimum()}, Max: {ui.timeoutSpinBox.maximum()}")
        
        # Vérifier les tables
        if hasattr(ui, 'tableWidget') and hasattr(ui, 'addTable'):
            main_columns = ui.tableWidget.columnCount()
            add_columns = ui.addTable.columnCount()
            if main_columns == 9 and add_columns == 8:
                print("  ✅ Tables configurées avec le bon nombre de colonnes")
                print(f"    - Table principale: {main_columns} colonnes (avec Password)")
                print(f"    - Table d'ajout: {add_columns} colonnes (avec Password)")
            else:
                print(f"  ❌ Nombre de colonnes incorrect. Principal: {main_columns}, Ajout: {add_columns}")
        
        # Vérifier les tooltips
        tooltip_elements = [
            ('autoCaptchaCheckBox', 'Résolution automatique des captchas'),
            ('fastModeCheckBox', 'Mode rapide - Utilise les cookies existants'),
            ('visaSubTypeComboBox', 'Type de demande'),
            ('timeoutSpinBox', 'Timeout réseau en secondes')
        ]
        
        for element_name, expected_tooltip in tooltip_elements:
            if hasattr(ui, element_name):
                element = getattr(ui, element_name)
                if expected_tooltip in element.toolTip():
                    print(f"  ✅ Tooltip {element_name} configuré")
                else:
                    print(f"  ⚠️  Tooltip {element_name} différent de l'attendu")
        
        print("✅ Test d'interface réussi!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test d'interface: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_app():
    """Test de l'application principale"""
    print("\n🚀 Test de l'application principale...")
    
    try:
        # Importer le module principal
        import main
        print("✅ Module main importé avec succès")
        
        # Vérifier que la classe MainWindow existe
        if hasattr(main, 'MainWindow'):
            print("✅ Classe MainWindow trouvée")
            return True
        else:
            print("❌ Classe MainWindow non trouvée")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors du test de l'application principale: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🧪 TEST DE L'INTERFACE GRAPHIQUE BLS 2025")
    print("=" * 50)
    
    # Vérifier que nous sommes dans le bon répertoire
    required_files = ['mygui.py', 'main.py', 'data.json']
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ Fichier requis manquant: {file}")
            print("Assurez-vous d'être dans le bon répertoire")
            return False
    
    print("✅ Tous les fichiers requis sont présents")
    
    # Exécuter les tests
    tests = [
        ("Interface graphique", test_interface),
        ("Application principale", test_main_app)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"💥 Erreur inattendue: {e}")
            results.append((test_name, False))
    
    # Résumé
    print("\n" + "=" * 50)
    print("📊 RÉSUMÉ DES TESTS D'INTERFACE")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHOUÉ"
        print(f"{test_name:.<30} {status}")
    
    print("-" * 50)
    print(f"Total: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 Interface prête à utiliser!")
        print("✅ Vous pouvez maintenant lancer: python main.py")
    else:
        print("⚠️  Certains tests ont échoué")
        print("🔧 Vérifiez les erreurs ci-dessus")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
