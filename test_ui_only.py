# -*- coding: utf-8 -*-
"""
Test simple de l'interface utilisateur uniquement
"""

import sys
import os

def test_ui_creation():
    """Test de création de l'interface utilisateur"""
    print("🖥️  Test de création de l'interface...")
    
    try:
        # Importer PyQt5 sans créer d'application
        from PyQt5.QtWidgets import QDialog
        from PyQt5.QtCore import QCoreApplication
        
        # Créer une application minimale
        if not QCoreApplication.instance():
            from PyQt5.QtWidgets import QApplication
            app = QApplication([])
        
        # Importer l'interface
        from mygui import Ui_Dialog
        print("✅ Module mygui importé avec succès")
        
        # Créer un dialog de test
        dialog = QDialog()
        ui = Ui_Dialog()
        
        # Tester setupUi - c'est ici que l'erreur se produisait
        print("🔧 Test de setupUi...")
        ui.setupUi(dialog)
        print("✅ setupUi exécuté sans erreur!")
        
        # Vérifier quelques éléments clés
        key_elements = ['startButton', 'deleteButton', 'saveButton', 'tableWidget']
        for element in key_elements:
            if hasattr(ui, element):
                print(f"  ✅ {element} créé")
            else:
                print(f"  ❌ {element} manquant")
                return False
        
        # Vérifier les nouvelles fonctionnalités
        new_elements = ['autoCaptchaCheckBox', 'fastModeCheckBox', 'visaSubTypeComboBox', 'timeoutSpinBox']
        for element in new_elements:
            if hasattr(ui, element):
                print(f"  ✅ Nouvelle fonctionnalité {element} créée")
            else:
                print(f"  ❌ Nouvelle fonctionnalité {element} manquante")
        
        print("✅ Interface créée avec succès!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la création de l'interface: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale"""
    print("🧪 TEST SIMPLE DE L'INTERFACE UI")
    print("=" * 40)
    
    # Vérifier les fichiers requis
    if not os.path.exists('mygui.py'):
        print("❌ Fichier mygui.py manquant")
        return False
    
    print("✅ Fichier mygui.py trouvé")
    
    # Tester la création de l'interface
    success = test_ui_creation()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 TEST RÉUSSI!")
        print("✅ L'interface peut être créée sans erreur")
        print("✅ Le problème 'deleteButton' a été corrigé")
    else:
        print("❌ TEST ÉCHOUÉ")
        print("🔧 Il y a encore des problèmes avec l'interface")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
