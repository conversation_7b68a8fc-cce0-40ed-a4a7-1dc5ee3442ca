#!/usr/bin/env python3
"""
Test du système ultra-rapide optimisé
"""

import time
from blsFacilateWork import BlsFacilateWork

def test_ultra_fast():
    """
    Test du système avec toutes les optimisations de vitesse
    """
    print("=== TEST SYSTÈME ULTRA-RAPIDE ===")
    
    try:
        # Créer une instance
        bls = BlsFacilateWork()
        
        print(f"⚡ Configuration ultra-rapide:")
        print(f"   🚀 network_timeout: {bls.network_timeout} secondes")
        print(f"   🔄 base_delay: {bls.base_delay} secondes")
        print(f"   📊 retry_count: {bls.retry_count}")
        print(f"   🤖 auto_captcha_enabled: {bls.auto_captcha_enabled}")
        print(f"   ⚡ fast_mode_enabled: {bls.fast_mode_enabled}")
        
        # Données du vrai compte
        test_email = "<EMAIL>"
        test_password = "Karim1978"
        test_data = [
            test_email,        # Index 0: Email
            test_password,     # Index 1: Password  
            "ALGER",          # Index 2: Place
            "3",              # Index 3: Member
            "",               # Index 4: N carte
            "",               # Index 5: CVV
            "",               # Index 6: Nom Carte
            ""                # Index 7: Expiry
        ]
        
        # Ajouter aux structures
        bls.data = {test_email: test_data}
        bls.Cookies = {test_email: None}
        
        print(f"✅ Compte configuré: {test_email}")
        print("🚀 Démarrage du processus ULTRA-RAPIDE...")
        print("📋 Optimisations actives:")
        print("   - Délais réduits au minimum (0.1-0.5s)")
        print("   - Timeouts ultra-courts (3-5s)")
        print("   - Options Chrome optimisées")
        print("   - Images désactivées")
        print("   - Plugins désactivés")
        print("   - Notifications bloquées")
        
        # Mesurer le temps avec précision
        start_time = time.perf_counter()
        
        print(f"\n⏱️  Démarrage à {time.strftime('%H:%M:%S')}")
        
        # Démarrer le processus
        bls.make_session(test_email, False)
        
        end_time = time.perf_counter()
        duration = end_time - start_time
        
        print(f"\n⏱️  Fin à {time.strftime('%H:%M:%S')}")
        print(f"🚀 Processus terminé en {duration:.2f} secondes!")
        
        # Analyse de performance
        if duration < 30:
            print("🏆 PERFORMANCE EXCELLENTE - Ultra-rapide !")
        elif duration < 60:
            print("✅ PERFORMANCE BONNE - Rapide")
        elif duration < 90:
            print("⚠️  PERFORMANCE MOYENNE - Acceptable")
        else:
            print("❌ PERFORMANCE LENTE - Besoin d'optimisation")
        
        print("\n📋 Le système ultra-rapide devrait:")
        print("   1. ✅ Naviguer instantanément vers BLS")
        print("   2. ✅ Se connecter en moins de 10 secondes")
        print("   3. ✅ Saisir email/mot de passe rapidement")
        print("   4. ✅ Détecter le captcha immédiatement")
        print("   5. ✅ Terminer le processus en moins de 30 secondes")
        
        # Recommandations
        print("\n💡 CONSEILS POUR VITESSE MAXIMALE:")
        print("   - Utilisez une connexion internet rapide")
        print("   - Fermez les autres applications Chrome")
        print("   - Résolvez le captcha rapidement quand il apparaît")
        print("   - Le système est maintenant optimisé au maximum")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ultra_fast()
