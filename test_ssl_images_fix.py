#!/usr/bin/env python3
"""
Test pour vérifier que SSL et images fonctionnent correctement
"""

import time
from blsFacilateWork import BlsFacilateWork

def test_ssl_and_images():
    """
    Test pour vérifier que le navigateur affiche correctement SSL et images
    """
    print("=== TEST SSL ET IMAGES ===")
    
    try:
        # Créer une instance
        bls = BlsFacilateWork()
        
        print(f"🔒 Configuration SSL et Images:")
        print(f"   ✅ Images AUTORISÉES (images: 1)")
        print(f"   ✅ SSL désactivé (verify_ssl: False)")
        print(f"   ✅ Options Chrome complètes")
        print(f"   ✅ Seleniumwire configuré")
        
        # Données du vrai compte
        test_email = "<EMAIL>"
        test_password = "Karim1978"
        test_data = [
            test_email,        # Index 0: Email
            test_password,     # Index 1: Password  
            "ALGER",          # Index 2: Place
            "3",              # Index 3: Member
            "",               # Index 4: N carte
            "",               # Index 5: CVV
            "",               # Index 6: Nom Carte
            ""                # Index 7: Expiry
        ]
        
        # Ajouter aux structures
        bls.data = {test_email: test_data}
        bls.Cookies = {test_email: None}
        
        print(f"✅ Compte configuré: {test_email}")
        print("🔒 Démarrage du test SSL et Images...")
        print("📋 Corrections appliquées:")
        print("   🖼️  Images: AUTORISÉES (plus bloquées)")
        print("   🔒 SSL: Configuration complète seleniumwire")
        print("   🛡️  Sécurité: Toutes les options Chrome SSL")
        print("   🚫 Avertissements: Supprimés avec --silent")
        
        # Démarrer le processus
        start_time = time.perf_counter()
        
        print(f"\n⏱️  Démarrage à {time.strftime('%H:%M:%S')}")
        print("🔍 VÉRIFICATIONS À FAIRE:")
        print("   1. 🔒 Barre d'adresse: Plus de 'Non sécurisé' rouge")
        print("   2. 🖼️  Images: Doivent s'afficher correctement")
        print("   3. 🎯 Captcha: Images des nombres visibles")
        print("   4. 🌐 Site BLS: Chargement complet avec images")
        
        # Démarrer le processus
        bls.make_session(test_email, False)
        
        end_time = time.perf_counter()
        duration = end_time - start_time
        
        print(f"\n⏱️  Fin à {time.strftime('%H:%M:%S')}")
        print(f"🔒 Test terminé en {duration:.2f} secondes!")
        
        print("\n📋 RÉSULTATS ATTENDUS:")
        print("   ✅ Barre d'adresse normale (pas de rouge)")
        print("   ✅ Images du site BLS visibles")
        print("   ✅ Logo BLS affiché")
        print("   ✅ Images du captcha visibles")
        print("   ✅ Navigation fluide sans avertissements")
        
        print("\n🎯 VÉRIFICATIONS MANUELLES:")
        print("   1. 🔍 Regardez la barre d'adresse")
        print("   2. 🖼️  Vérifiez que les images s'affichent")
        print("   3. 🎨 Vérifiez que le design du site est complet")
        print("   4. 🔢 Vérifiez que les nombres du captcha sont visibles")
        
        if duration < 60:
            print("🏆 PERFORMANCE: Excellent (< 60s)")
        elif duration < 90:
            print("✅ PERFORMANCE: Bon (< 90s)")
        else:
            print("⚠️  PERFORMANCE: Acceptable (> 90s)")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()

def show_configuration():
    """
    Affiche la configuration SSL et images
    """
    print("=== CONFIGURATION SSL ET IMAGES ===")
    
    print("\n🖼️  CONFIGURATION IMAGES:")
    print("   ✅ images: 1 (AUTORISÉ - les images s'affichent)")
    print("   ❌ images: 2 (BLOQUÉ - les images ne s'affichent pas)")
    print("   📝 Statut actuel: AUTORISÉ")
    
    print("\n🔒 CONFIGURATION SSL:")
    print("   ✅ verify_ssl: False")
    print("   ✅ suppress_connection_errors: True")
    print("   ✅ auto_config: False")
    print("   ✅ ca_cert: None")
    print("   ✅ ca_key: None")
    
    print("\n🛡️  OPTIONS CHROME SSL:")
    ssl_options = [
        "--ignore-certificate-errors",
        "--ignore-ssl-errors",
        "--disable-web-security",
        "--allow-running-insecure-content",
        "--reduce-security-for-testing",
        "--test-type",
        "--silent",
        "--no-first-run",
        "--disable-logging"
    ]
    
    for option in ssl_options:
        print(f"   ✅ {option}")
    
    print("\n💡 OBJECTIFS:")
    print("   🎯 Éliminer 'Non sécurisé' en rouge")
    print("   🎯 Afficher toutes les images")
    print("   🎯 Captcha avec nombres visibles")
    print("   🎯 Navigation fluide")

if __name__ == "__main__":
    show_configuration()
    print("\n" + "="*50 + "\n")
    test_ssl_and_images()
