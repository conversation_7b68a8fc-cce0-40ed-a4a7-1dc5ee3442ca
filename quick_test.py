# -*- coding: utf-8 -*-
"""
Test rapide pour vérifier la correction
"""

def check_open_bls_code():
    """Vérifier le code de open_bls"""
    print("🔍 Vérification du code open_bls...")
    
    try:
        with open('blsFacilateWork.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Rechercher la méthode open_bls
        lines = content.split('\n')
        in_open_bls = False
        open_bls_lines = []
        
        for line in lines:
            if 'def open_bls(' in line:
                in_open_bls = True
                open_bls_lines.append(line)
            elif in_open_bls:
                if line.strip().startswith('def ') and 'open_bls' not in line:
                    break
                open_bls_lines.append(line)
        
        open_bls_code = '\n'.join(open_bls_lines)
        
        print("✅ Code open_bls extrait")
        
        # Vérifications importantes
        checks = [
            ("Navigation chrome.get présente", "chrome.get(" in open_bls_code),
            ("URL BLS présente", "algeria.blsspainglobal.com" in open_bls_code),
            ("Gestion is_open", "is_open = True" in open_bls_code),
            ("Condition auto", "if not auto" in open_bls_code)
        ]
        
        print("\n📋 Vérifications:")
        all_good = True
        for check_name, condition in checks:
            if condition:
                print(f"  ✅ {check_name}")
            else:
                print(f"  ❌ {check_name}")
                all_good = False
        
        # Analyser la structure de la boucle while
        print("\n🔍 Analyse de la boucle while:")
        while_found = False
        is_open_before_nav = False
        is_open_after_nav = False
        
        for i, line in enumerate(open_bls_lines):
            if 'while not is_open:' in line:
                while_found = True
                print(f"  ✅ Boucle while trouvée ligne {i+1}")
            
            if while_found and 'is_open = True' in line:
                # Vérifier si c'est avant ou après navigation
                prev_lines = open_bls_lines[max(0, i-10):i]
                has_navigation_before = any('chrome.get(' in prev_line for prev_line in prev_lines)
                
                if has_navigation_before:
                    is_open_after_nav = True
                    print(f"  ✅ is_open = True APRÈS navigation (ligne {i+1})")
                else:
                    is_open_before_nav = True
                    print(f"  ⚠️  is_open = True AVANT navigation (ligne {i+1})")
        
        if is_open_after_nav and not is_open_before_nav:
            print("  ✅ Structure correcte: is_open défini après navigation")
            return True
        else:
            print("  ❌ Structure incorrecte: is_open mal placé")
            return False
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    """Fonction principale"""
    print("🧪 TEST RAPIDE DE CORRECTION")
    print("=" * 40)
    
    success = check_open_bls_code()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 CORRECTION VALIDÉE!")
        print("✅ Le code a été corrigé correctement")
        print("🚀 Testez maintenant:")
        print("   1. python main.py")
        print("   2. Cliquez sur 'Open' pour un compte")
        print("   3. Le navigateur devrait aller sur algeria.blsspainglobal.com")
    else:
        print("❌ PROBLÈME DÉTECTÉ")
        print("🔧 Le code nécessite encore des corrections")
    
    print("\n📋 Résumé de la correction:")
    print("  • Problème: is_open = True était défini AVANT navigation")
    print("  • Solution: Déplacer is_open = True APRÈS navigation")
    print("  • Résultat: Navigation garantie avant sortie de boucle")

if __name__ == "__main__":
    main()
