#!/usr/bin/env python3
"""
Test pour trouver la bonne URL BLS
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options

def test_bls_urls():
    """
    Test différentes URLs BLS pour trouver la bonne
    """
    print("=== Test des URLs BLS ===")
    
    # URLs à tester basées sur vos captures d'écran
    urls_to_test = [
        "https://algeria.blsspainglobal.com/DZA/bls/vtv9850",
        "https://algeria.blsspainglobal.com/DZA/bls/",
        "https://algeria.blsspainglobal.com/DZA/",
        "https://algeria.blsspainglobal.com/",
        "https://algeria.blsspainglobal.com/DZA/bls/vtv",
        "https://algeria.blsspainglobal.com/DZA/bls/home",
        "https://algeria.blsspainglobal.com/DZA/bls/index"
    ]
    
    try:
        # Configuration Chrome
        chrome_options = Options()
        chrome_options.add_experimental_option("excludeSwitches", ['enable-automation'])
        chrome_options.add_argument("--disable-gpu")
        
        # Créer le driver
        driver = webdriver.Chrome(options=chrome_options)
        
        for url in urls_to_test:
            print(f"\n🔍 Test de l'URL: {url}")
            
            try:
                driver.get(url)
                time.sleep(3)
                
                current_url = driver.current_url
                title = driver.title
                
                print(f"  ✅ URL finale: {current_url}")
                print(f"  📄 Titre: {title}")
                
                # Vérifier si on trouve des éléments BLS typiques
                if "BLS" in title or "spain" in title.lower() or "visa" in title.lower():
                    print(f"  🎯 Cette URL semble correcte!")
                    
                    # Vérifier si on trouve des boutons de réservation
                    try:
                        book_elements = driver.find_elements("xpath", "//*[contains(text(), 'Book')]")
                        appointment_elements = driver.find_elements("xpath", "//*[contains(text(), 'Appointment')]")
                        
                        if book_elements or appointment_elements:
                            print(f"  🎉 Éléments de réservation trouvés!")
                            print(f"     - Boutons 'Book': {len(book_elements)}")
                            print(f"     - Éléments 'Appointment': {len(appointment_elements)}")
                        
                    except Exception as e:
                        print(f"  ⚠️ Erreur lors de la recherche d'éléments: {e}")
                
                # Si c'est une redirection, noter l'URL finale
                if current_url != url:
                    print(f"  🔄 Redirection détectée vers: {current_url}")
                
            except Exception as e:
                print(f"  ❌ Erreur: {e}")
        
        print(f"\n⏳ Gardons le navigateur ouvert 15 secondes pour inspection...")
        time.sleep(15)
        
        driver.quit()
        print("✅ Test terminé.")
        
    except Exception as e:
        print(f"❌ Erreur générale: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_bls_urls()
