from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5 import uic 
from blsFacilateWork import BlsFacilateWork
import sys
import os
from PyQt5.QtWidgets import QApplication, QPushButton, QVBoxLayout, QWidget
from PyQt5.QtWidgets import QApplication, QMainWindow, QTableWidget, QTableWidgetItem, QPushButton
from PyQt5.QtWidgets import QApplication, QMainWindow
import json
import threading
from mygui import Ui_Dialog
import traceback
import logging

# Configuration du logging pour le débogage
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Chargement sécurisé des fichiers JSON
try:
    with open('data.json','r') as f:
        data = json.loads(f.read())
except FileNotFoundError:
    logger.warning("data.json non trouvé, création d'un nouveau fichier")
    data = {}
    with open('data.json', 'w') as f:
        json.dump(data, f, indent=2)
except json.JSONDecodeError:
    logger.error("Erreur de format dans data.json, création d'un nouveau fichier")
    data = {}
    with open('data.json', 'w') as f:
        json.dump(data, f, indent=2)

try:
    with open('cookies.json','r') as f2:
        cookies = json.loads(f2.read())
except FileNotFoundError:
    logger.warning("cookies.json non trouvé, création d'un nouveau fichier")
    cookies = {}
    with open('cookies.json', 'w') as f2:
        json.dump(cookies, f2, indent=2)
except json.JSONDecodeError:
    logger.error("Erreur de format dans cookies.json, création d'un nouveau fichier")
    cookies = {}
    with open('cookies.json', 'w') as f2:
        json.dump(cookies, f2, indent=2)

user_session = BlsFacilateWork()

class MyGUI(QMainWindow, QDialog):
    def __init__(self):
        super().__init__()
        try:
            self.ui = Ui_Dialog()
            self.ui.setupUi(self)
            
            # Connexion des signaux
            self.ui.startButton.clicked.connect(self.start_auto)
            self.ui.addButton.clicked.connect(self.add_new)
            self.ui.deleteButton.clicked.connect(self.delete_row)
            self.ui.saveButton.clicked.connect(self.save_table)
            self.ui.loginAllCheckBox.stateChanged.connect(self.on_loginAllCheckBox_state_changed)
            self.ui.manualCheckBox.stateChanged.connect(self.on_manualCheckBox_state_changed)
            self.ui.selfieCheckBox.stateChanged.connect(self.on_selfieCheckBox_state_changed)
            self.ui.paymentCheckBox.stateChanged.connect(self.on_paymentCheckBox_state_changed)
            
            # Configuration du combo box
            self.ui.calenderBox.addItem('3 times only')
            self.ui.calenderBox.addItem('all green')
            self.ui.calenderBox.addItem('30 days')
            self.ui.calenderBox.currentIndexChanged.connect(self.on_combo_box_index_changed)

            self.load_emails()
            logger.info("Interface initialisée avec succès")
            
        except Exception as e:
            logger.error(f"Erreur lors de l'initialisation: {str(e)}")
            traceback.print_exc()
    
    def on_selfieCheckBox_state_changed(self, state):
        try:
            if state == 2:  # Qt.Checked
                user_session.selfieFill = True
                logger.debug("selfieFill activé")
            else:
                user_session.selfieFill = False
                logger.debug("selfieFill désactivé")
        except Exception as e:
            logger.error(f"Erreur dans on_selfieCheckBox_state_changed: {str(e)}")
    
    def on_paymentCheckBox_state_changed(self, state):
        try:
            if state == 2:  # Qt.Checked
                user_session.paymentFill = True
                logger.debug("paymentFill activé")
            else:
                user_session.paymentFill = False
                logger.debug("paymentFill désactivé")
        except Exception as e:
            logger.error(f"Erreur dans on_paymentCheckBox_state_changed: {str(e)}")
    
    def on_combo_box_index_changed(self, index):
        try:
            selected_item = self.ui.calenderBox.currentText()
            if selected_item == '3 times only':
                user_session.dateGetType = 0
            elif selected_item == 'all green':
                user_session.dateGetType = 1
            elif selected_item == '30 days':
                user_session.dateGetType = 2
            logger.debug(f"dateGetType changé vers: {user_session.dateGetType}")
        except Exception as e:
            logger.error(f"Erreur dans on_combo_box_index_changed: {str(e)}")
    
    def on_loginAllCheckBox_state_changed(self, state):
        try:
            if state == 2:  # Qt.Checked
                user_session.login_all = True
                logger.debug("login_all activé")
            else:
                user_session.login_all = False
                logger.debug("login_all désactivé")
        except Exception as e:
            logger.error(f"Erreur dans on_loginAllCheckBox_state_changed: {str(e)}")
    
    def on_manualCheckBox_state_changed(self, state):
        try:
            if state == 2:  # Qt.Checked
                user_session.all_manual = True
                logger.debug("all_manual activé")
            else:
                user_session.all_manual = False
                logger.debug("all_manual désactivé")
        except Exception as e:
            logger.error(f"Erreur dans on_manualCheckBox_state_changed: {str(e)}")

    def delete_row(self):
        try:
            selected_items = self.ui.tableWidget.selectedItems()
            if not selected_items:
                logger.warning("Aucune ligne sélectionnée pour la suppression")
                return
                
            selected_rows = set()

            # Obtenir les indices des lignes sélectionnées
            for item in selected_items:
                selected_rows.add(item.row())

            # Supprimer les lignes sélectionnées
            emails_to_delete = []
            for row in selected_rows:
                email_item = self.ui.tableWidget.item(row, 0)
                if email_item is not None and email_item.text().strip() != "":
                    emails_to_delete.append(email_item.text().strip())

            # Supprimer du dictionnaire data
            for email in emails_to_delete:
                if email in data:
                    del data[email]
                    logger.debug(f"Suppression de {email} des données")
                if email in cookies:
                    del cookies[email]
                    logger.debug(f"Suppression de {email} des cookies")

            # Supprimer les lignes du tableau (en ordre décroissant)
            for row in sorted(selected_rows, reverse=True):
                self.ui.tableWidget.removeRow(row)
            
            # Sauvegarder dans les fichiers JSON
            with open('data.json', 'w') as f3:
                json.dump(data, f3, indent=2)
            with open('cookies.json', 'w') as f4:
                json.dump(cookies, f4, indent=2)
                
            logger.info(f"Supprimé {len(emails_to_delete)} lignes avec succès")
            
        except Exception as e:
            logger.error(f"Erreur dans delete_row: {str(e)}")
            traceback.print_exc()

    def save_table(self):
        try:
            row_count = self.ui.tableWidget.rowCount()
            saved_count = 0
            
            for i in range(row_count):
                # Récupérer l'email (clé)
                email_item = self.ui.tableWidget.item(i, 0)
                if email_item is None or email_item.text().strip() == "":
                    logger.warning(f"Ligne {i} ignorée: pas d'email")
                    continue
                    
                email = email_item.text().strip()
                
                # Récupérer toutes les données de la ligne
                row_data = []
                for j in range(7):
                    item = self.ui.tableWidget.item(i, j)
                    if item is None:
                        row_data.append("")
                    else:
                        row_data.append(item.text().strip())
                
                data[email] = row_data
                saved_count += 1
            
            # Sauvegarder dans le fichier JSON
            with open('data.json', 'w') as f3:
                json.dump(data, f3, indent=2)
                
            logger.info(f"Sauvegarde réussie: {saved_count} lignes sauvegardées")
            
        except Exception as e:
            logger.error(f"Erreur dans save_table: {str(e)}")
            traceback.print_exc()

    def add_new(self):
        try:
            logger.debug("Début de l'ajout d'une nouvelle ligne")
            
            # Vérifier si addTable a au moins une ligne
            if self.ui.addTable.rowCount() == 0:
                logger.error("addTable est vide")
                return
                
            # Vérifier si l'email (première colonne) existe
            email_item = self.ui.addTable.item(0, 0)
            if email_item is None or email_item.text() is None or email_item.text().strip() == "":
                logger.error("Email requis pour ajouter une nouvelle ligne")
                return
                
            email = email_item.text().strip()
            logger.debug(f"Tentative d'ajout de l'email: {email}")
            
            # Vérifier si l'email existe déjà
            if email in data:
                logger.error(f"Email {email} existe déjà dans les données")
                return
            
            # Initialiser le dictionnaire session_chrome s'il n'existe pas
            if not hasattr(user_session, 'session_chrome'):
                user_session.session_chrome = {}
                logger.debug("Initialisation du dictionnaire session_chrome dans add_new")
            
            row_count = self.ui.tableWidget.rowCount()
            self.ui.tableWidget.insertRow(row_count)
            
            # Ajouter les données de manière sécurisée
            row_data = []
            for i in range(7):
                item = self.ui.addTable.item(0, i)
                if item is None or item.text() is None:
                    cell_value = ""
                else:
                    cell_value = item.text().strip()
                
                self.ui.tableWidget.setItem(row_count, i, QTableWidgetItem(cell_value))
                row_data.append(cell_value)
                logger.debug(f"Colonne {i}: '{cell_value}'")
            
            # Initialiser l'entrée session_chrome pour ce nouvel email
            user_session.session_chrome[email] = None
            logger.debug(f"Initialisation de session_chrome[{email}] = None")
            
            # Créer le bouton "Open"
            button = QPushButton("Open")
            button.clicked.connect(lambda checked, idx=email, btn=button: self.handle_button_clicked(idx, btn))
            self.ui.tableWidget.setCellWidget(row_count, 8, button)
            
            # Sauvegarder dans data et cookies
            data[email] = row_data
            cookies[email] = None
            
            # Sauvegarder dans les fichiers JSON
            with open('data.json', 'w') as f3:
                json.dump(data, f3, indent=2)
            with open('cookies.json', 'w') as f4:
                json.dump(cookies, f4, indent=2)
                
            logger.info(f"Email {email} ajouté avec succès")
            
            # Optionnel: Vider addTable après ajout réussi
            for i in range(7):
                item = self.ui.addTable.item(0, i)
                if item is not None:
                    item.setText("")
            
        except Exception as e:
            logger.error(f"Erreur dans add_new: {str(e)}")
            traceback.print_exc()

    def start_auto(self):
        try:
            if self.ui.startButton.text() == "Start Notifications":
                self.ui.startButton.setText("Stop Notifications")
                self.ui.startButton.repaint()
                thread1 = threading.Thread(target=user_session.go_auto, args=())
                thread1.daemon = True  # Thread se ferme avec l'application
                thread1.start()
                logger.info("Notifications automatiques démarrées")
            else:
                self.ui.startButton.setText("Start Notifications")
                self.ui.startButton.repaint()
                user_session.stop_auto = True
                logger.info("Arrêt des notifications automatiques demandé")
        except Exception as e:
            logger.error(f"Erreur dans start_auto: {str(e)}")
            traceback.print_exc()

    def load_emails(self):
        try:
            logger.debug("Chargement des emails dans l'interface")
            
            # Initialiser le dictionnaire session_chrome s'il n'existe pas
            if not hasattr(user_session, 'session_chrome'):
                user_session.session_chrome = {}
                logger.debug("Initialisation du dictionnaire session_chrome dans load_emails")
            
            self.ui.tableWidget.setRowCount(len(data))
            self.ui.tableWidget.setColumnCount(9)
            self.ui.tableWidget.setHorizontalHeaderLabels(('Email','Password','Place','Member','N carte','CVV','Nom Carte','Expiry','Open/Close'))

            self.ui.addTable.setRowCount(1)
            self.ui.addTable.setColumnCount(8)
            self.ui.addTable.setHorizontalHeaderLabels(('Email','Password','Place','Member','N carte','CVV','Nom Carte','Expiry'))
            
            index = 0
            for d in data:
                try:
                    # Vérifier que data[d] a assez d'éléments
                    if len(data[d]) < 7:
                        logger.warning(f"Données incomplètes pour {d}, ajout de valeurs vides")
                        while len(data[d]) < 7:
                            data[d].append("")
                    
                    for j in range(7):
                        self.ui.tableWidget.setItem(index, j, QTableWidgetItem(str(data[d][j])))
                    
                    # Initialiser l'entrée session_chrome pour cet email
                    if d not in user_session.session_chrome:
                        user_session.session_chrome[d] = None
                        logger.debug(f"Initialisation de session_chrome[{d}] = None")
                    
                    button = QPushButton("Open")
                    button.clicked.connect(lambda checked, idx=d, btn=button: self.handle_button_clicked(idx, btn))
                    self.ui.tableWidget.setCellWidget(index, 8, button)
                    index += 1
                    
                except Exception as e:
                    logger.error(f"Erreur lors du chargement de {d}: {str(e)}")
                    continue
            
            logger.info(f"Chargement terminé: {index} emails chargés")
            
        except Exception as e:
            logger.error(f"Erreur dans load_emails: {str(e)}")
            traceback.print_exc()

    def handle_button_clicked(self, index, button):
        try:
            if button.text() == "Open":
                button.setText("Close")
                button.repaint()
                
                # Initialiser le dictionnaire session_chrome s'il n'existe pas
                if not hasattr(user_session, 'session_chrome'):
                    user_session.session_chrome = {}
                    logger.debug("Initialisation du dictionnaire session_chrome")
                
                # Initialiser l'entrée pour cet utilisateur s'elle n'existe pas
                if index not in user_session.session_chrome:
                    user_session.session_chrome[index] = None
                    logger.debug(f"Initialisation de session_chrome[{index}] = None")
                
                # Utiliser la nouvelle méthode ULTRA-RAPIDE avec captcha automatique
                thread = threading.Thread(target=user_session.ultra_fast_complete_session, args=(index,))
                thread.daemon = True
                thread.start()
                logger.info(f"Session ouverte pour {index}")
                
            else:
                button.setText("Open")
                button.repaint()
                
                # Vérifier et fermer la session si elle existe
                if (hasattr(user_session, 'session_chrome') and 
                    index in user_session.session_chrome and 
                    user_session.session_chrome[index] is not None):
                    try:
                        user_session.session_chrome[index].close()
                        user_session.session_chrome[index] = None
                        logger.info(f"Session fermée pour {index}")
                    except Exception as close_error:
                        logger.warning(f"Erreur lors de la fermeture de session pour {index}: {close_error}")
                        user_session.session_chrome[index] = None
                        
        except Exception as e:
            logger.error(f"Erreur dans handle_button_clicked pour {index}: {str(e)}")
            traceback.print_exc()
            # Réinitialiser le bouton en cas d'erreur
            button.setText("Open")
            button.repaint()

def main():
    try:
        app = QApplication(sys.argv)
        window = MyGUI()
        window.show()
        logger.info("Application démarrée avec succès")
        sys.exit(app.exec_())
    except Exception as e:
        logger.error(f"Erreur critique dans main: {str(e)}")
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()