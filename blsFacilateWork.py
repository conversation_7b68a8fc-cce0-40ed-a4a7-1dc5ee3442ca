import asyncio
from telegram import Bo<PERSON>
from telegram import InputFile
class MyCustomError(Exception):
    pass
class BlsFacilateWork:
    
    from selenium.webdriver.support.wait import WebDriverWait
    from seleniumwire import webdriver
    from selenium.webdriver.common.keys import Keys
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.wait import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.common.action_chains import ActionChains
    from PIL import Image
    import io
    import time
    from datetime import datetime, date # Added for time slot checking
    #from undetected_chromedriver import Chrome
    from undetected_chromedriver import Chrome, ChromeOptions
    from selenium.webdriver.support.ui import Select
    import json
    from selenium.webdriver.common.alert import Alert
    from selenium.common.exceptions import TimeoutException # Added for error handling
    #import pygame
    from bs4 import BeautifulSoup
    import json
    import queue
    from urllib.parse import quote,urlparse, parse_qs, unquote

    def get_available_date_hours(self,headerr,bodyy,session_number):
        from h2spacex import H2OnTlsConnection
        from time import sleep
        from h2spacex import h2_frames
        import re
        
        h2_conn = H2OnTlsConnection(
    hostname='algeria.blsspainglobal.com',
    port_number=443
)

        h2_conn.setup_connection()
        headers = headerr
        body = bodyy
        stream_ids_list = [1,3,5,7,9,11,13,15,17,19,21,23,25,27,29,31,33,35,37,39,41,43,45,47,49,51,53,55,57,59]
        self.key_date_list= {'2024-02-21': 43, '2024-02-22': 45, '2024-02-23': 47, '2024-02-24': 49, '2024-02-25': 51, '2024-02-28': 53, '2024-02-29': 55, '2024-02-30': 57, '2024-02-31': 59, '2024-03-01': 1, '2024-03-04': 3, '2024-03-05': 5, '2024-03-06': 7, '2024-03-07': 9, '2024-03-08': 11, '2024-03-11': 13, '2024-03-12': 15, '2024-03-13': 17, '2024-03-14': 19, '2024-03-15': 21, '2024-03-18': 23, '2024-03-19': 25, '2024-03-20': 27, '2024-03-21': 29, '2024-03-22': 31, '2024-03-25': 33, '2024-03-26': 35, '2024-03-27': 37, '2024-03-28': 39, '2024-03-29': 41}
        date_key_list = {43:"2024-02-21",45:"2024-02-22",47:"2024-02-23",49:"2024-02-24",51:"2024-02-25",53:"2024-02-28",55:"2024-02-29",57:"2024-02-30",59:"2024-02-31",1:"2024-03-01",3:"2024-03-04",5:"2024-03-05",7:"2024-03-06",9:"2024-03-07",11:"2024-03-08",13:"2024-03-11",15:"2024-03-12",17:"2024-03-13",19:"2024-03-14",21:"2024-03-15",23:"2024-03-18",25:"2024-03-19",27:"2024-03-20",29:"2024-03-21",31:"2024-03-22",33:"2024-03-25",35:"2024-03-26",37:"2024-03-27",39:"2024-03-28",41:"2024-03-29"}
        
        date_key_list_2 = {1:"2024-02-24",3:"2024-02-02",5:"2024-02-03",7:"2024-02-04",9:"2024-02-07",11:"2024-02-08",13:"2024-02-9",15:"2024-02-10",17:"2024-02-11",19:"2024-02-14",21:"2024-02-15",23:"2024-02-18",25:"2024-02-21",27:"2024-02-2",29:"2024-02-23",31:"2024-02-01",33:"2024-02-25",35:"2024-02-28",37:"2024-02-29",39:"2024-02-30",41:"2024-02-31"}



        all_headers_frames = []  # all headers frame + data frames which have not the last byte
        all_data_frames = []
        for s_id in stream_ids_list:
            new_body = modified_url = re.sub(r"(AppointmentDate=)\d{4}-\d{2}-\d{2}", rf"AppointmentDate={date_key_list[s_id]}", body)
    
            header_frames_without_last_byte, last_data_frame_with_last_byte = h2_conn.create_single_packet_http2_post_request_frames(
        method='POST',
        headers_string=headers,
        scheme='https',
        stream_id=s_id,
        authority="algeria.blsspainglobal.com",
        body=new_body,
        path='/DZA/blsappointment/gasd'
    )
    
            all_headers_frames.append(header_frames_without_last_byte)
            all_data_frames.append(last_data_frame_with_last_byte)
        temp_headers_bytes = b''
        for h in all_headers_frames:
            temp_headers_bytes += bytes(h)
            
        temp_data_bytes = b''
        for d in all_data_frames:
            temp_data_bytes += bytes(d) 
        h2_conn.send_frames(temp_headers_bytes)

# wait some time
        sleep(0.1)
        h2_conn.send_frames(temp_data_bytes)
        resp = h2_conn.read_response_from_socket(_timeout=10)
        self.frame_parser[session_number] = h2_frames.FrameParser(h2_connection=h2_conn)
        self.frame_parser[session_number].add_frames(resp)
        for sid in stream_ids_list:
            try:
                print(f"------ headers for date {date_key_list[sid]}")
                print(type(self.frame_parser[session_number].headers_and_data_frames[sid]['header']))
                print(self.frame_parser[session_number].headers_and_data_frames[sid]['header'])
                print(f"------ response for date {date_key_list[sid]}")
                print(self.frame_parser[session_number].headers_and_data_frames[sid]['data'])
                print("----------------------   ----------------------------")
            except:
                pass
#frame_parser.show_response_of_sent_requests()

# close the connection to stop response parsing and exit the script
        h2_conn.close_connection()
    def modify_response(self,request, response,session_number):
        from urllib.parse import quote
        import re
        from bs4 import BeautifulSoup
        if request.method == 'GET' and   '/DZA/CaptchaPublic/GenerateCaptcha' in request.path and self.auto_captcha :
            original_body = response.body
            soup = BeautifulSoup(original_body, 'html.parser')
            input_element = soup.find_all('input', {'name': '__RequestVerificationToken'})

# Extract the value attribute
            if input_element[-1]:
                self.token_value = input_element[-1]['value']
                print("Token Value:", self.token_value)
            else:
                print("Input element not found.")
        
        
        if request.method == 'GET' and (request.path.upper()=="/DZA/ACCOUNT/LOGIN" ) and self.all_manual:
            original_body = response.body
            email = b'type = "text" value="{}"'.replace(b'{}',self.data[session_number][0].encode('utf-8'))
            modified_body = original_body.replace(b'type = "text"', email)
            password = b'type="password" value="{}"'.replace(b'{}',self.data[session_number][1].encode('utf-8'))
            modified_body = modified_body.replace(b'type="password"', password)
            response.body = modified_body
            
        if request.method == 'POST' and   '/DZA/bls/vt8809/' in request.path:
            
            self.all_date[session_number] = False
            location_value = "0566245a-7ba1-4b5a-b03b-3dd33e051f46" if self.data[session_number][1] == "alger" else "8457a52e-98be-4860-88fc-2ce11b80a75e"
            appointmentFor_value = "Individual" if self.data[session_number][2] == "1" else "Family"   
            
            value= "/DZA/blsappointment/manageappointment/a838?appointmentFor="+appointmentFor_value+"&applicantsNo="+self.data[session_number][2]+"&visaType=c805c157-7e8f-4932-89cf-d7ab69e1af96&visaSubType=b563f6e3-58c2-48c4-ab37-a00145bfce7c&appointmentCategory="+self.visa_category[session_number]+"&location="+location_value+"&missionId=&data="+str(self.captcha_data[session_number])
            
            vv = b'{"success":true,"available":true,"returnUrl":"{}"}'.replace(b'{}',value.encode('utf-8'))
            
            if b"returnUrl" in response.body and b"appointment" in response.body :
                self.available_date=True
            
            #print(value)
            
            #response.body = vv
        if (("cib.satim.dz" in request.url )and (request.method == 'GET') and (response.headers.get("Content-Type", "").startswith("text/html")) and self.paymentFill) :
            print("in cib.satim.dz")
            print(request.path)
            
            original_body = response.body
            ipan = b'id="iPAN" value="{}"'.replace(b'{}',self.data[session_number][3].encode('utf-8'))
            modified_body = original_body.replace(b'id="iPAN"', ipan)
            icvc = b'id="iCVC" value="{}"'.replace(b'{}',self.data[session_number][4].encode('utf-8'))
            modified_body = modified_body.replace(b'id="iCVC"', icvc)
            itext = b'id="iTEXT" value="{}"'.replace(b'{}',self.data[session_number][5].encode('utf-8'))
            modified_body = modified_body.replace(b'id="iTEXT"', itext)
            expiration_date = b'Expiration Date {}  '.replace(b'{}',self.data[session_number][6].encode('utf-8'))
            modified_body = modified_body.replace(b'Expiration Date', expiration_date)
            
            response.body = modified_body
            
            print("-----------response.body--------------")
            print(response.body)
            
            print("------------modified_body--------")
            print(modified_body)
            
        if  "/js/site.js" in request.url and request.method == 'GET' and False:   
            original_body = response.body
            
            modified_body = original_body.replace(b'document.getElementById("global-overlay").style.display = "block";', b'document.getElementById("global-overlay").style.display = "none";')
            response.body = modified_body
        if request.method == 'POST' and   request.path=='/DZA/query/UploadProfileImage':
            original_body = response.body.decode('utf-8')
            j = self.json.loads(original_body)
            #print(j["fileId"])
            if j["fileId"] :
                if len(self.data[session_number]) != 8:
                    self.data[session_number].append(j["fileId"])
                else:
                    self.data[session_number][7]=j["fileId"]
                    
                with open('data.json', 'w') as f4:
                    self.json.dump(self.data, f4)
            
        
        
        if  "/DZA/blsAppointment/ManageAppointment".upper() in request.url.upper() and response.headers.get("Content-Type", "").startswith("text/html") :
            self.all_date[session_number] = False
            original_body = response.body
            modified_body = original_body.replace(b'enable(false)', b'enable(true)')
            modified_body = modified_body.replace(b'valid == false', b'false')
            modified_body = modified_body.replace(b'$("#btnVerifyAppointment").hide();', b'')
            modified_body = modified_body.replace(b'$("#btnVerifyEmail").hide();', b'')
            modified_body = modified_body.replace(b'$(".upload-photo-btn").hide();', b'')
            modified_body = modified_body.replace(b'$("#btnVerifyApplicant").hide();', b'')
            if len(self.data[session_number])==8 :
                erre = b'/DZA/query/getfile?fileid={}'.replace(b'{}',self.data[session_number][7].encode('utf-8'))
                modified_body = modified_body.replace(b'/assets/images/avatar/01.jpg', erre)
                erre = b'name="ApplicantPhotoId" type="hidden" value="{}"'.replace(b'{}',self.data[session_number][7].encode('utf-8'))
                modified_body = modified_body.replace(b'name="ApplicantPhotoId" type="hidden" value=""', erre)
            
            response.body = modified_body
            
        if request.method == 'POST' and '/DZA/blsappointment/gasd' in request.path:
            
            response_content = response.body.decode('utf-8')
            #print(response_content)
            print("-------- request header ------")
            print(request.headers)
            print("-------- request body --------")
            print(request.body)
            
            print("the true response")
            print(response.body)
            print("-----the true response------")
            
            pattern = r'AppointmentDate=([^\&]+)'

# Use re.search to find the first match in the string
            match = re.search(pattern, request.body.decode('utf-8'))
            
            appointment_date = match.group(1)
            print("Appointment Date:", appointment_date)
            #request.path= "/dza/bls"
            
            
            

            if self.dateGetType == 1:
                response_to_send = b'[{"Name":"08:00-08:30","Value":null,"Code":"1","Count":1,"EnumId":0,"Error":null,"DataType":null,"ClassName":null,"title":null,"key":null,"lazy":false,"selected":false,"DepartmentOwnerUserId":null,"HasChildren":false,"UserId":null,"Id":"08:00-08:30","CreatedDate":"0001-01-01T00:00:00","CreatedBy":null,"LastUpdatedDate":"0001-01-01T00:00:00","LastUpdatedBy":null,"IsDeleted":false,"SequenceOrder":null,"CompanyId":null,"LegalEntityId":null,"DataAction":0,"Status":0,"VersionNo":0,"PortalId":null},{"Name":"08:30-09:00","Value":null,"Code":"1","Count":1,"EnumId":0,"Error":null,"DataType":null,"ClassName":null,"title":null,"key":null,"lazy":false,"selected":false,"DepartmentOwnerUserId":null,"HasChildren":false,"UserId":null,"Id":"08:30-09:00","CreatedDate":"0001-01-01T00:00:00","CreatedBy":null,"LastUpdatedDate":"0001-01-01T00:00:00","LastUpdatedBy":null,"IsDeleted":false,"SequenceOrder":null,"CompanyId":null,"LegalEntityId":null,"DataAction":0,"Status":0,"VersionNo":0,"PortalId":null},{"Name":"09:00-09:30","Value":null,"Code":"1","Count":1,"EnumId":0,"Error":null,"DataType":null,"ClassName":null,"title":null,"key":null,"lazy":false,"selected":false,"DepartmentOwnerUserId":null,"HasChildren":false,"UserId":null,"Id":"09:00-09:30","CreatedDate":"0001-01-01T00:00:00","CreatedBy":null,"LastUpdatedDate":"0001-01-01T00:00:00","LastUpdatedBy":null,"IsDeleted":false,"SequenceOrder":null,"CompanyId":null,"LegalEntityId":null,"DataAction":0,"Status":0,"VersionNo":0,"PortalId":null},{"Name":"09:30-10:00","Value":null,"Code":"1","Count":1,"EnumId":0,"Error":null,"DataType":null,"ClassName":null,"title":null,"key":null,"lazy":false,"selected":false,"DepartmentOwnerUserId":null,"HasChildren":false,"UserId":null,"Id":"09:30-10:00","CreatedDate":"0001-01-01T00:00:00","CreatedBy":null,"LastUpdatedDate":"0001-01-01T00:00:00","LastUpdatedBy":null,"IsDeleted":false,"SequenceOrder":null,"CompanyId":null,"LegalEntityId":null,"DataAction":0,"Status":0,"VersionNo":0,"PortalId":null},{"Name":"10:00-10:30","Value":null,"Code":"1","Count":1,"EnumId":0,"Error":null,"DataType":null,"ClassName":null,"title":null,"key":null,"lazy":false,"selected":false,"DepartmentOwnerUserId":null,"HasChildren":false,"UserId":null,"Id":"10:00-10:30","CreatedDate":"0001-01-01T00:00:00","CreatedBy":null,"LastUpdatedDate":"0001-01-01T00:00:00","LastUpdatedBy":null,"IsDeleted":false,"SequenceOrder":null,"CompanyId":null,"LegalEntityId":null,"DataAction":0,"Status":0,"VersionNo":0,"PortalId":null},{"Name":"10:30-11:00","Value":null,"Code":"1","Count":1,"EnumId":0,"Error":null,"DataType":null,"ClassName":null,"title":null,"key":null,"lazy":false,"selected":false,"DepartmentOwnerUserId":null,"HasChildren":false,"UserId":null,"Id":"10:30-11:00","CreatedDate":"0001-01-01T00:00:00","CreatedBy":null,"LastUpdatedDate":"0001-01-01T00:00:00","LastUpdatedBy":null,"IsDeleted":false,"SequenceOrder":null,"CompanyId":null,"LegalEntityId":null,"DataAction":0,"Status":0,"VersionNo":0,"PortalId":null},{"Name":"11:00-11:30","Value":null,"Code":"1","Count":1,"EnumId":0,"Error":null,"DataType":null,"ClassName":null,"title":null,"key":null,"lazy":false,"selected":false,"DepartmentOwnerUserId":null,"HasChildren":false,"UserId":null,"Id":"11:00-11:30","CreatedDate":"0001-01-01T00:00:00","CreatedBy":null,"LastUpdatedDate":"0001-01-01T00:00:00","LastUpdatedBy":null,"IsDeleted":false,"SequenceOrder":null,"CompanyId":null,"LegalEntityId":null,"DataAction":0,"Status":0,"VersionNo":0,"PortalId":null},{"Name":"11:30-12:00","Value":null,"Code":"1","Count":1,"EnumId":0,"Error":null,"DataType":null,"ClassName":null,"title":null,"key":null,"lazy":false,"selected":false,"DepartmentOwnerUserId":null,"HasChildren":false,"UserId":null,"Id":"11:30-12:00","CreatedDate":"0001-01-01T00:00:00","CreatedBy":null,"LastUpdatedDate":"0001-01-01T00:00:00","LastUpdatedBy":null,"IsDeleted":false,"SequenceOrder":null,"CompanyId":null,"LegalEntityId":null,"DataAction":0,"Status":0,"VersionNo":0,"PortalId":null}]'
                response.body=response_to_send 
                
                
            if self.all_date[session_number] == False and self.dateGetType == 2:
            
                self.get_available_date_hours(str(request.headers),request.body.decode('utf-8'),session_number)
                self.all_date[session_number]  = True
                response.body=self.frame_parser[session_number].headers_and_data_frames[self.key_date_list[appointment_date]]['data']
                multiline_string = self.frame_parser[session_number].headers_and_data_frames[self.key_date_list[appointment_date]]['header']
                lines = multiline_string.splitlines()

# Extract all lines except the first one
                response.status_code = 200
                remaining_lines = "\n".join(lines[1:])
                #print(remaining_lines)
                new_headers = dict(re.findall(r'(?P<name>.*?): (?P<value>.*?)\n', remaining_lines))
                #response.headers.clear()
                for name, value in new_headers.items():
                    response.headers[name] = value
            elif self.all_date[session_number] == True and self.dateGetType == 2:
                response.body=self.frame_parser[session_number].headers_and_data_frames[self.key_date_list[appointment_date]]['data']
                multiline_string = self.frame_parser[session_number].headers_and_data_frames[self.key_date_list[appointment_date]]['header']
                lines = multiline_string.splitlines()

    # Extract all lines except the first one
                response.status_code = 200
                remaining_lines = "\n".join(lines[1:])
                    #print(remaining_lines)
                new_headers = dict(re.findall(r'(?P<name>.*?): (?P<value>.*?)\n', remaining_lines))
                    #response.headers.clear()
                for name, value in new_headers.items():
                    response.headers[name] = value
                
            
        
               
        
    def interceptor(self,request,session_number):
        from urllib.parse import quote
        import http.client
        import re
        import subprocess
        import urllib.parse
        import threading
    # Check if the request path matches '/DZA/bls/visatype'
    
    
        if request.method ==    'POST' and '/DZA/CaptchaPublic/SubmitCaptcha' in request.path and self.auto_captcha :
            new_body = b'SelectedImages=nvzfagp%2Cslcfz%2Cokbpks&Id=1edaac76-1a34-4815-9112-482f6efc47b5&Captcha=Bf0KU6r4PHzEtR9My6uzzPdKSddwylXruf9ExVC2AqwgiR5ycEqqKD0n6sTVxpXFAMEiyxKbKypeIJeRKluBctR3LnnxxPJy2rnOI%2BvCTXd%2FdFEObgxYW8YwyGW58oGBY3%2BnQ87uJvgs3HZgc%2BZOft1fFK82dImahOv4G4ZaWzOqa%2FP%2F5MCDtejXzT9Oz0ZR7ADLJ6J%2BMzD2LrB8OZpKBsr5JdNjSEfcIQHHX2aY%2Fc4Ax%2BXw%2BFLWvYTC4N6oeceaAWvVATxJpBxADKkI79Ltu0o1Mw6cF2lgS8IwQsXuzLTQYCnRbl7D1dh8O556BQackiPdUnRtfWHbsnpXSESSH%2FJfofZ%2FkIZak4qxQ6%2BBthlxsg6H2hVJx%2B44GdBwkoDN4V7E47kPAlSRiZtJUzoyozyG8rvqKeXwbucRyLBywkuntGcq0k%2BIi1JFe6RGqjjMNaZhtN6Tu1TNkmbkgWDN9INioEUgYRpcKO%2BMNCDJh62yWwsZQOOetq3FVlxmCs3lwsy3LJJfUI8DkK3KY9b2T87JmHPvRgur9zY5prh3MyYPTjUKMFd20qkQenYtXOrQi9aM3tUBRzffyydaO6aWjy0iF5km9WXBZKBdG07NY0SUBkd55Ay4Sl1HWmb7UCmPN4u2I90HWPSj2GT8pd2BSRJLuiCkekZ4Db5OCiUx%2BHiCU9Tmsbbk05oXQ5Gd1O%2FenEaa4blRkizW0zwohCUY8Kz8fD%2BSEUPeoubqMCi%2BK%2FlYjxygULdORM06dKLsRkfmpQYbloVKO8rfCU6V3am9HNVR6Et90HLWLlrymwAvSZGgW8hfteLQPA6NHfbsgOq4inPZfarrjy0tseo1a%2Fr55zlHmKVmPY%2BM3LOkfO3cluI7GQBy3FXR1Y5NkKb8hfcS%2FV77k95fgLob%2BYs5s6Nj1fFirhrQfWuYi%2FJZ3Vi6rMUnAfU2%2FuECs3Ffsk%2BQCNTnjq1mekfwlMOL2u4H%2BqEzXchmwAp2gOQg%2FYd2%2B4zFGe%2BCnsKzuFS4Sfl9vMlZnXM%2BANn1eQoENjjjwM0dQmV4ls7CIa4gv7cGPD2WZuM0Wh%2F9gKSDmuCBkApFpozNz5Y29yPXMZ1Iydj6erDWMy9%2B3Ibjn4OxVSCLHAAAK74EYLzeauXLJ2NuTAmtusGVBDHGQfSxhE3J34%2BZsP8Yq62k5xYWBUxLcJSqCMHHXXyFQ9wiTc8u9PEuuuNuVX4Bst%2B7L8pXoaXoBCAMe75I%2BVWCb6XP1mGJcKDfM9AoomVBoVFNyu8Hj4ttqQQ0uconXigmDkVWzETWi4CYTyEhqN5fTqQ2eh34VOHROP2lgZ1NU6w5I7OHsB7sGL0L26Vk3NOcSXKrrF2BSWwB788YyYolr%2BN3lqEax0EMAa4udX22tY1hhhDG2GUOim7Xqnc1Vx8%2FkJk68Bu0fBb4mjidm5XFrG7Ou1ud1shJvclDVgFUTBG1oQC%2Fk0O%2FW%2Fu3k4Z14J9trPoQExN8E6KcxtHIDTyjxz7AFroXaJqLHlD9CEYeDF8wkaXSZb8vWmbfAQwmOPcI6tW5R40XcPKqOMW6CKX4XqoKAu%2FB2Yq0qerLkBucZiJJcWsS4t1Sv5phTD92TYGJzJjWKVujJXE0hXJR6ijVnOEOoL4dg2DSWJAT3rCot2QvqwZACpni3sBcH9b3O36CscHpVS5K0ltWwoFfpockbAynFZMutAc3t7L8SmwSzxdxa4qRVc0RRE5LTbIoOXv2f4X3NDxqA0injEJEkbnd7bpD1hw9EMAtEcJSISvG83qEAqoVN%2FLiV5aS3DOTRTRVtUbC7gw5eDn7l0RlNWB95Zezel55UkjI4cSXUJlGWZ1RWZH4SMoE4QgJFHvRLVdZn%2FPl4%2FnOzfind3Z7%2B2uVv6oL08KOUyTApBh78SrViMyVpeENAjR%2Bo4oYE1YNKyhQPE%2BIqX6KE7%2BrmnpWi8yXrONvOcBIIEQaiYWJpV2T81n%2F%2Bom7goKZe8uz3GDpcuem3HFhtfakGG27ek3L4iO%2Btva4Kx4IE3ANSjJ2zz%2FHWy1%2BUz5DpCPSdXeGjIWiR3HleZztADA4S0VtavwQydbPcYNOQfpj6eDyvdptHepq3hmPgbZPu6Jzfy3MLSQNCmH2d4df%2FbhjvQTJenv%2Faam37cI4YOyPysFYLlalD3PGnBA14pZOre1yUAPEho7GamK0iMIm7cW50pZaci9181esybkFCwoF%2FRkl2GN5Eq5uNKhjUix0gukgczd0wWKb2KqYiPVBKt6ZIIraRTgbcZOKAB4K6emO3oIJZHrv%2ByjiFES0ORmjUeDkQbHyG1M88e97KV8ORBqMWUVtXggJgKeTLJ87KWY5OMhpA2sbxusdvGijcIGMK9XVCbPGzWdnDMDweaUoHIbejeas5rbuQpJckFY1i6BO0lqSuLcxkj18w%2FTi7LZs7E%2FgTCwDaMHRznROV6ahFMx1JpTHF%2Bx9GyIY6OkCWq5bd5G1KbXw72W00VsJGJtot3tZbdULGKlibhgTNa%2BYQdawEHAAPtfl7Xw0ATu0q9b97e5IMJF01HgGfGUs%2BRFnNX3Moy4TJD2liowbdK7TNG9hTyy9hxmuUvo1hkx2sYZihykxmbTYh%2Bk%2Bs8ml%2FuWlrbqIujwYjCwHF%2BVy%2BJRpO%2FqIyXWVEJgo6iM%2FZYcccUvVpbmkB9UYObYsKq%2BTrAeCeAhqupVrFMrzFpbSN1iX3UVL20FNpGbKiX8YAOIoezL9i2TiTWaf2UKv%2BTY%3D&__RequestVerificationToken={}&X-Requested-With=XMLHttpRequest'
            final_body = new_body.replace(b'{}',self.token_value.encode())
            print(final_body)
            request.body = final_body
            del request.headers['Content-Length']
            request.headers['Content-Length'] = str(len(request.body))
        if request.method ==    'POST' and '/DZA/blsappointment/gasd' in request.path:
            if self.dateGetType == 2:
                request.abort()
            
        if request.method == 'GET' and 'stopauto' in request.path:
            self.stop_auto =True
            request.abort()
            
        if request.method == 'POST' and ("/DZA/bls/vt/" in request.path ) :
            if b"********-9bb4-477d-918b-4809370190b9" in request.body:
                self.visa_category[session_number] = "********-9bb4-477d-918b-4809370190b9"
            elif b"37ba2fe4-4551-4c7d-be6e-5214617295a9" in request.body :
                self.visa_category[session_number] = "37ba2fe4-4551-4c7d-be6e-5214617295a9"
            else :
                self.visa_category[session_number] = "5c2e8e01-796d-4347-95ae-0c95a9177b26"
                
            
        
        if request.method == 'POST' and (request.path=="/DZA/account/loginPost" ) :
            cookiess = self.session_chrome[session_number].get_cookies()
            list_cookies=""
            for cookie_value in cookiess:
                list_cookies+= f"{cookie_value['name']}={cookie_value['value']}; "
            print(cookiess)
            request.path="/DZA/account/loGinPost"
            if self.login_all:
                the_email_passed = quote(self.data[session_number][0], safe='')
                original_string = request.body.decode('utf-8')
                for i in self.data:
           
                    print("--- the body loGin ---  " + str(i))
                    the_email = quote(self.data[i][0], safe='')
                    modified_string = original_string.replace(the_email_passed, the_email)
                    #print(modified_string)
                    #print(the_email)
                    curl_command = f"curl -X POST -H 'Cookie: {list_cookies}' -H 'User-Agent: Google Chrome/112.0.5615.49 Linux' -H 'Origin: https://algeria.blsspainglobal.com'-H 'Referer: https://algeria.blsspainglobal.com/DZA/Account/LogIn?ReturnUrl=%2FDZA%2Fbls%2Fvtv9850' -d '{modified_string}' https://algeria.blsspainglobal.com/DZA/account/loGinPost -i"
                    print(curl_command)
                    import io
        # Execute the curl command using subprocess
        
                    result = subprocess.run(curl_command, shell=True, check=True, text=True, capture_output=True)
            
                    set_cookie_headers = re.findall(r'set-cookie:(.*?)\n', result.stdout, re.I)
                    print('set_cookie_headers')
                    print(result.stdout)
            #print(set_cookie_headers)

                    cookies = {}
                    for header in set_cookie_headers:
                        header = header.strip()
                        cookie_attributes = header.split(';')

            # Split the first attribute into name and value
                        name, value = cookie_attributes[0].strip().split('=', 1) if '=' in cookie_attributes[0] else (cookie_attributes[0].strip(), '')

                # Add the cookie to the dictionary
                        cookies[name] = value
            
                    for cookie_name, cookie_value in cookies.items():
                        print(f"Cookie Name: {cookie_name}")
                        print(f"Cookie Value: {cookie_value}")
                        self.cookie = {
            "name'": cookie_name,
            "value": cookie_value,
            "domain": "algeria.blsspainglobal.com" 
        }  
                    self.Cookies[i]= {
            "name": cookie_name,
            "value": cookie_value,
            "domain": "algeria.blsspainglobal.com"}
            #self.loggedIn = True
                print(self.Cookies)
                with open('cookies.json', 'w') as f3:
                        self.json.dump(self.Cookies, f3) 
            
        if "gototime" in request.path:
            self.all_date[session_number] = False
            location_value = "0566245a-7ba1-4b5a-b03b-3dd33e051f46" if self.data[session_number][1] == "alger" else "8457a52e-98be-4860-88fc-2ce11b80a75e"
            appointmentFor_value = "Individual" if self.data[session_number][2] == "1" else "Family"
            
            request.path= "/DZA/blsAppointment/ManageAppointment/9df8?appointmentFor="+appointmentFor_value+"&applicantsNo="+self.data[session_number][2]+"&visaType=c805c157-7e8f-4932-89cf-d7ab69e1af96&visaSubType=b563f6e3-58c2-48c4-ab37-a00145bfce7c&appointmentCategory="+self.visa_category[session_number]+"&location="+location_value+"&missionId=&data="+str(self.captcha_data[session_number])
            print(request.path)
        if '/DZA/bls/vt8809/' in request.path and request.method == 'GET':
                # Extracting query parameters
            #request.path = "/DZA/bls/vt/30e10953"
            query_params = request.params

                # Check if 'data' parameter exists and modify its value
            if 'data' in query_params:
                    # Modify the value of 'data' parameter
                    #query_params['data'] = ['new_value']

                    # Update the params in the request
                    #request.params = query_params
                self.dataToTime=quote(query_params['data'], safe='')
                self.captcha_data[session_number] = quote(query_params['data'], safe='')
                print("data:", self.dataToTime)
           
        if request.method == 'POST' and (request.path=="/DZA/blsappointment/SubmitLivenessDetection" or request.path=="/LivenessDetection" or request.path== "/DZA/blsappointment/livenessdetection" or "liveness".upper() in request.path.upper()) and self.selfieFill:
            print(str(self.current_user)+ '  from intercept')
            import re
                
            original_request_body = request.body
            matches = re.finditer(b' name="([^"]+)"', original_request_body)
            name_value_pairs = []
            content_type_header = request.headers.get('Content-Type')
            boundary_match = re.search(r'boundary=([^\s;]+)', content_type_header)
            boundary = boundary_match.group(1)
            request_body = b''
            for match in matches:
                pair = match.group(1)
                name_value_pairs.append(pair)

            print(name_value_pairs) 
            request_body += b'--' + boundary.encode('utf-8') + b'\r\n'  
            for name in name_value_pairs:
                            
                if name != b"image1" and name != b"image2":
                    value = re.search(rb'(?<="'+ re.escape(name) + rb'"\r\n\r\n).*?(?=\r\n-+)', original_request_body)  
                    if value:
                        request_body += b'Content-Disposition: form-data; name="'+re.escape(name)+b'"\r\n\r\n' 
                        request_body += value.group(0) + b'\r\n'
                            #if name == b"isMobile" :
                                #request_body += b'--' + boundary.encode('utf-8') + b'--\r\n'
                            #else:
                               # request_body += b'--' + boundary.encode('utf-8') + b'\r\n'
                        value = value.group(0).decode('utf-8')  # Convert matched bytes to a string
                        print(f"Value for {name}: {value}")
                    else:
                        print(f"No value found for {name}")
                if name == b"image1" :
                    request_body += b'Content-Disposition: form-data; name="image1"; filename="blob"\r\n'
                    request_body += b'Content-Type: image/png\r\n\r\n'
                    with open("./BlsImg/"+self.data[session_number][0]+"1.png", 'rb') as image1:
                        request_body += image1.read() + b'\r\n'
                            #request_body += b'--' + boundary.encode('utf-8') + b'\r\n'
                if name == b"image2" :
                    request_body += b'Content-Disposition: form-data; name="image2"; filename="blob"\r\n'
                    request_body += b'Content-Type: image/png\r\n\r\n'
                    with open("./BlsImg/"+ self.data[session_number][0] +"2.png", 'rb') as image2:
                        request_body += image2.read() + b'\r\n'
                            #request_body += b'--' + boundary.encode('utf-8') + b'\r\n'
                if name == name_value_pairs[-1] :
                    request_body += b'--' + boundary.encode('utf-8') + b'--\r\n'
                else:
                    request_body += b'--' + boundary.encode('utf-8') + b'\r\n'
                        
                    
                            
                            #if value:
                                #value = value.group(0).decode('utf-8')  # Convert matched bytes to a string
                                #print(f"Value for {name}: {value}")
                            #else:
                                #print(f"No value found for {name}")
            request.body = request_body
            #print(request.headers)
            #print(request.body)
            print ("imges passed for  :"  + self.data[session_number][0])
            del request.headers['Content-Length']
            request.headers['Content-Length'] = str(len(request.body))

    def migrate_data_structure(self):
        """
        Migrates the old data structure to the new one with password.
        """
        updated_data = {}
        for email, user_data in self.data.items():
            if len(user_data) == 7:  # Old structure (email, location, members, card_number, cvv, card_name, expiry)
                # Insert default password at position 1
                new_data = [user_data[0]]  # email
                new_data.append("A2z0@I0z0")  # default password
                new_data.extend(user_data[1:])  # rest of the data
                updated_data[email] = new_data
            else:
                updated_data[email] = user_data
        
        self.data = updated_data
        
        # Save the new structure
        with open('data.json', 'w') as f:
            self.json.dump(self.data, f, indent=2)

    def __init__(self):
        # Import des modules nécessaires pour les nouvelles fonctionnalités
        import logging

        # Configuration du logger - Réduire les logs de debug seleniumwire
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

        # Réduire les logs verbeux de seleniumwire et hpack
        logging.getLogger('seleniumwire').setLevel(logging.WARNING)
        logging.getLogger('hpack').setLevel(logging.WARNING)
        logging.getLogger('h2').setLevel(logging.WARNING)

        self.logger = logging.getLogger(__name__)

        # Chargement des données
        with open('data.json','r') as f:
            self.data = self.json.loads(f.read())
        with open('cookies.json','r') as f2:
            self.Cookies = self.json.loads(f2.read())

        # Migration de la structure de données si nécessaire
        self.migrate_data_structure()

        # Variables existantes
        self.current_user=-1
        self.key_date_list = None
        self.captcha_solved =False
        self.loggedIn =False

        self.cookie = None
        self.session_opened = False
        self.available_date = False
        self.session_finish = False

        # Nouvelles variables pour les fonctionnalités 2025
        self.auto_captcha_enabled = True
        self.fast_mode_enabled = True  # Activer le mode rapide
        self.visa_subtype = "ALG1"  # Défaut: première demande
        self.network_timeout = 5  # Timeout ultra-rapide
        self.retry_count = 2  # Moins de tentatives
        self.base_delay = 0.1  # Délai minimal
        self.current_apointment_category = "Normal"
        self.available_apointment_category = ""
        self.opened_session = 1
        self.step =0
        self.token1=''
        self.token2=''
        self.decoded_token1=""
        self.captcha_list=[]
        self.captcha_list_old=[]
        self.session_chrome={}
        self.captcha_data={}
        self.visa_category={}
        self.all_date = {}
        self.frame_parser = {}
        for key in self.data:
            self.session_chrome[key]=None
            self.captcha_data[key]=None
            self.visa_category[key]=None
            self.all_date[key]=False
            self.frame_parser[key]=None
            # S'assurer que chaque utilisateur a une entrée dans Cookies
            if key not in self.Cookies:
                self.Cookies[key] = None
            
        
        
        self.dataToTime=""
        self.fifo = self.queue.Queue(maxsize=10)
        
        self.stop_auto = False
        
        self.dateGetType = 0
        self.login_all = False
        self.all_manual = False  # Mode automatique par défaut pour continuer le processus
        self.selfieFill = True
        self.paymentFill = True
        self.fast_mode = True  # Activer le mode rapide pour accélérer
        self.auto_captcha = True  # Activer la résolution automatique de captcha
        self.use_new_login_system = True  # Utiliser le nouveau système de connexion

        # Initialiser les variables nécessaires pour le captcha
        self.position = {'x': 0, 'y': 0}
        self.size = {'width': 800, 'height': 600}
        self.iframe = None
        self.token_value = ""
        self.auto_captcha = False

    def validate_application_type(self, chrome, user):
        """
        Valide le type de demande selon les nouvelles règles BLS 2025
        """
        try:
            self.logger.info(f"Validation du type de demande pour {user}")

            # Vérifier le type de visa précédent
            previous_visa_element = self.WebDriverWait(chrome, 5).until(
                self.EC.presence_of_element_located((self.By.XPATH, "//select[@id='previousVisaType']"))
            )

            # Logique de validation selon l'historique
            if len(self.data[user]) > 8:  # Si données d'historique disponibles
                # Appliquer la catégorisation basée sur le dernier visa Schengen
                self.logger.info("Application de la catégorisation basée sur l'historique Schengen")

            return True

        except self.TimeoutException:
            self.logger.warning(f"Élément de validation du type de demande non trouvé pour {user}")
            return False
        except Exception as e:
            self.logger.warning(f"Validation du type de demande échouée pour {user}: {e}")
            return False

    def check_time_slots_availability(self, chrome):
        """
        Vérifie la disponibilité selon les nouvelles règles (20h quotidienne)
        """
        from datetime import datetime, time, date

        current_time = datetime.now().time()
        target_time = time(20, 0)  # 20h00

        if current_time < target_time:
            wait_seconds = (datetime.combine(date.today(), target_time) -
                           datetime.combine(date.today(), current_time)).seconds
            self.logger.info(f"Attente jusqu'à 20h : {wait_seconds} secondes")
            self.time.sleep(wait_seconds)

        self.logger.info("Vérification des créneaux disponibles à 20h")
        return True

    def select_visa_subtype(self, chrome, user):
        """
        Sélectionne le sous-type de visa approprié
        """
        subtype_mapping = {
            "first_application": "ALG1",
            "renewal": "ALG2",
            "family_reunion": "ALG3",
            "business": "ALG4"
        }

        try:
            # Logique de sélection basée sur les données utilisateur
            if len(self.data[user]) > 8:  # Si sous-type spécifié
                selected_subtype = self.data[user][8]
            else:
                selected_subtype = self.visa_subtype  # Utilise la valeur par défaut

            self.logger.info(f"Sélection du sous-type de visa: {selected_subtype} pour {user}")

            # Rechercher et sélectionner le sous-type dans l'interface
            subtype_element = self.WebDriverWait(chrome, 10).until(
                self.EC.presence_of_element_located((self.By.XPATH, f"//option[contains(text(), '{selected_subtype}')]"))
            )
            subtype_element.click()

            return True

        except Exception as e:
            self.logger.error(f"Erreur lors de la sélection du sous-type de visa: {e}")
            return False

    def retry_with_backoff(self, func, max_retries=None, base_delay=None):
        """
        Réessaie une fonction avec délai exponentiel
        """
        if max_retries is None:
            max_retries = self.retry_count
        if base_delay is None:
            base_delay = self.base_delay

        for attempt in range(max_retries):
            try:
                return func()
            except MyCustomError as e:
                if attempt == max_retries - 1:
                    raise e
                delay = base_delay * (2 ** attempt)
                self.logger.warning(f"Tentative {attempt + 1} échouée, retry dans {delay}s")
                self.time.sleep(delay)

    def detect_site_changes(self, chrome):
        """
        Détecte les changements dans la structure du site
        """
        try:
            # Vérifier la présence d'éléments critiques
            critical_elements = [
                "btnVerify",
                "btnSubmit",
                "#calendarContainer"
            ]

            for element_id in critical_elements:
                try:
                    self.WebDriverWait(chrome, 2).until(
                        self.EC.presence_of_element_located((self.By.CSS_SELECTOR, element_id))
                    )
                except self.TimeoutException:
                    self.logger.warning(f"Élément critique manquant: {element_id}")
                    # Envoyer notification que le site a changé
                    self.asyncio.run(self.send_telegram_message(
                        f"🚨 Site BLS modifié - Élément manquant: {element_id}"
                    ))
                    return False

            return True

        except Exception as e:
            self.logger.error(f"Erreur détection changements: {e}")
            return False

    def make_session(self,user_id,auto):
        import http.client
        import re
        import subprocess
        import urllib.parse
        import threading
        #print(curl_command)
        import io
                # Execute the curl command using subprocess
        import urllib.parse
        import threading
        self.user_id = user_id

        # Initialiser les structures pour ce user_id si elles n'existent pas
        if user_id not in self.session_chrome:
            self.session_chrome[user_id] = None
            self.captcha_data[user_id] = None
            self.visa_category[user_id] = None
            self.all_date[user_id] = False
            self.frame_parser[user_id] = None

        chrome_options = self.ChromeOptions()
        chrome_options.add_experimental_option("excludeSwitches", ['enable-automation'])
        #chrome_options.add_argument("--headless")  # Setting the browser to run in headless mode
        chrome_options.add_argument("--disable-gpu")

        # Options complètes pour résoudre le problème "Non sécurisé" avec seleniumwire
        chrome_options.add_argument("--ignore-certificate-errors")
        chrome_options.add_argument("--ignore-ssl-errors")
        chrome_options.add_argument("--ignore-certificate-errors-spki-list")
        chrome_options.add_argument("--ignore-urlfetcher-cert-requests")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--allow-running-insecure-content")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-features=VizDisplayCompositor")

        # Options SSL spécifiques pour seleniumwire - SOLUTION COMPLÈTE
        chrome_options.add_argument("--ignore-certificate-errors-spki-list")
        chrome_options.add_argument("--ignore-ssl-errors-spki-list")
        chrome_options.add_argument("--allow-running-insecure-content")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--reduce-security-for-testing")
        chrome_options.add_argument("--ignore-certificate-errors")
        chrome_options.add_argument("--ignore-urlfetcher-cert-requests")
        chrome_options.add_argument("--ignore-ssl-errors")
        chrome_options.add_argument("--test-type")
        chrome_options.add_argument("--disable-extensions-file-access-check")
        chrome_options.add_argument("--disable-extensions-http-throttling")

        # Options supplémentaires pour corriger "Non sécurisé" avec seleniumwire
        chrome_options.add_argument("--allow-running-insecure-content")
        chrome_options.add_argument("--disable-features=VizDisplayCompositor,TranslateUI")
        chrome_options.add_argument("--disable-ipc-flooding-protection")
        chrome_options.add_argument("--disable-renderer-backgrounding")
        chrome_options.add_argument("--disable-backgrounding-occluded-windows")
        chrome_options.add_argument("--disable-background-timer-throttling")
        chrome_options.add_argument("--disable-field-trial-config")
        chrome_options.add_argument("--disable-back-forward-cache")
        chrome_options.add_argument("--disable-background-networking")
        chrome_options.add_argument("--disable-sync")
        chrome_options.add_argument("--disable-translate")
        chrome_options.add_argument("--disable-default-apps")
        chrome_options.add_argument("--no-first-run")
        chrome_options.add_argument("--no-default-browser-check")
        chrome_options.add_argument("--disable-logging")
        chrome_options.add_argument("--disable-gpu-logging")
        chrome_options.add_argument("--silent")

        # Optimisations pour vitesse maximale
        chrome_options.add_argument("--disable-plugins")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-background-timer-throttling")
        chrome_options.add_argument("--disable-renderer-backgrounding")
        chrome_options.add_argument("--disable-backgrounding-occluded-windows")
        chrome_options.add_argument("--disable-features=TranslateUI")
        chrome_options.add_argument("--disable-ipc-flooding-protection")

        # Préférences optimisées SANS bloquer les images
        prefs = {
            "profile.default_content_setting_values": {
                "notifications": 2,  # Bloquer les notifications
                "media_stream": 2,   # Bloquer l'accès caméra/micro
                "images": 1,         # AUTORISER les images (1 = autorisé, 2 = bloqué)
            },
            # Options SSL pour éviter "Non sécurisé"
            "profile.default_content_settings.popups": 0,
            "profile.content_settings.exceptions.automatic_downloads.*.setting": 1,
            "profile.default_content_setting_values.automatic_downloads": 1,
        }
        chrome_options.add_experimental_option("prefs", prefs)

        # Options expérimentales pour SSL
        chrome_options.add_experimental_option("useAutomationExtension", False)
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])

        # Désactiver les avertissements de sécurité
        chrome_options.add_argument("--disable-infobars")
        chrome_options.add_argument("--disable-notifications")
        chrome_options.add_argument("--disable-popup-blocking")

        # FORCER Chrome à démarrer DIRECTEMENT sur l'URL (pas de data:)
        login_url = "https://algeria.blsspainglobal.com/DZA/Account/LogIn?ReturnUrl=%2FDZA%2Fappointment%2Fnewappointment"
        chrome_options.add_argument(f"--app={login_url}")  # FORCER démarrage direct sur URL
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_argument("--no-first-run")
        chrome_options.add_argument("--no-default-browser-check")
        chrome_options.add_argument("--disable-default-apps")
        chrome_options.add_argument("--disable-background-mode")
        chrome_options.add_argument("--disable-background-networking")
        chrome_options.add_argument("--disable-web-security")  # Pour éviter les blocages
        chrome_options.add_argument("--disable-features=VizDisplayCompositor")

        if self.session_chrome[user_id] == None:
            # Configuration seleniumwire COMPLÈTE pour éviter "Non sécurisé"
            seleniumwire_options = {
                'verify_ssl': False,                    # Désactiver vérification SSL
                'suppress_connection_errors': True,     # Supprimer erreurs de connexion
                'ignore_http_methods': [],              # Ignorer toutes les méthodes HTTP
                'disable_encoding': True,               # Désactiver encodage
                'auto_config': False,                   # Pas de configuration automatique
                'addr': '127.0.0.1',                   # Adresse locale
                'port': 0,                             # Port automatique
                'ca_cert': None,                       # Pas de certificat CA
                'ca_key': None,                        # Pas de clé CA
            }
            # Créer le navigateur - Il démarre DIRECTEMENT sur l'URL grâce à --app=
            self.session_chrome[user_id] = self.webdriver.Chrome(
                options=chrome_options,
                seleniumwire_options=seleniumwire_options
            )

            # PLUS BESOIN de navigation - Chrome démarre directement sur la bonne URL !
            # Attendre juste que la page se charge
            self.time.sleep(1)

            # INJECTION IMMÉDIATE de l'email DÈS que Chrome est ouvert
            self.ultra_fast_login(self.session_chrome[user_id], user_id)
        self.session_chrome[user_id].request_interceptor = lambda request: self.interceptor(request,user_id)
        self.session_chrome[user_id].response_interceptor = lambda request , response: self.modify_response(request,response,user_id) 
        thread = threading.Thread(target=self.open_bls, args=(self.session_chrome[user_id], user_id,auto))
        thread.start()
        thread.join()

        
        
       
                        
    def go_auto(self):
        emails=[]
        for email in self.data:
            emails.append(email)
        current_session = 0
        self.stop_auto = False
        while  self.available_date  == False and self.stop_auto == False:
            
            if current_session==len(emails) or current_session == 3  :
                current_session=0
            
            try :
                self.make_session(emails[current_session],True)
                self.time.sleep(60)
            except:
                current_session -=1
            current_session +=1  
                
            
         
    def login(self,chrome,user):
        import http.client
        import re
        import subprocess
        import urllib.parse
        import threading
        #self.chrome.get('https://algeria.blsspainglobal.com/DZA/account/login')
        max_retry_duration = 10
        start_time = self.time.time()
        while self.time.time() - start_time < max_retry_duration:
            try:
                # Your logic to open the session, for example:
                chrome.get('https://algeria.blsspainglobal.com/DZA/account/login')
                
                # Define the condition you want to wait for, e.g., a specific element to be present
                verify_selection = self.WebDriverWait(chrome, 10).until(self.EC.presence_of_element_located((self.By.ID, "btnVerify")))
                
                # If the above line is reached, the session is open, and you can break out of the loop
                break
            except Exception as e:
                # Handle any exceptions, or you can simply wait and retry
                print("Session not opened yet. Retrying...")
                #raise MyCustomError("This is a custom error message")

        
        
        verify_selection.click()
        self.iframe = self.WebDriverWait(chrome, 10).until(self.EC.presence_of_element_located((self.By.CSS_SELECTOR,".k-widget.k-window")))

# Get the position (left and top) and size (width and height) of the element
        self.position = self.iframe.location
        self.size = self.iframe.size
            
        chrome.switch_to.frame(0)
        
        while len(self.captcha_list)!=10 or self.captcha_passed == False:
            if len(self.captcha_list)!=10 and len(self.captcha_list)!=0:
                repeat_captcha = self.WebDriverWait(chrome, 10).until(self.EC.presence_of_element_located((self.By.XPATH,"/html/body/div/div[2]/div[2]/div/form/div[2]/div[2]")))
                #/html/body/div/div[2]/div[2]/div/form/div[2]/div[2]/p
                repeat_captcha.click()
                
                print('in while')
            #self.which_part=1
            my_thread1 = threading.Thread(target=self.get_Captcha,args=(chrome,))
            my_thread1.start()
            my_thread1.join()
            print(self.captcha_list)
            if len(self.captcha_list)==10 :
                self.click_captcha(chrome,False)
                
        self.captcha_passed = False
        list_input = []
        hidden_xpath = "/html/body/main/main/div/div/div[2]/div[2]/form/div[7]/label"
        jjj=0               #/html/body/main/main/div/div/div[2]/form/div[35]/div/span
        for l in range(2, 22):
           
           hidden_modified = hidden_xpath.replace("[7]", f"[{l}]")
           if jjj==2 :
               break
            #print(modified_xpath)
           try:
               hidden_apointment_category = self.WebDriverWait(chrome, 0.01).until(self.EC.presence_of_element_located((self.By.XPATH,hidden_modified)))
               
               hidden_apointment_category.click()
               list_input.append(l)
               jjj+=1
               #print(l)
           except:
               pass
        
        hidden_modified = hidden_xpath.replace("[7]", f"[{list_input[0]}]")
        hidden_apointment_email=   self.WebDriverWait(chrome, 0.01).until(self.EC.presence_of_element_located((self.By.XPATH,hidden_modified)))
        hidden_apointment_email.click()
           
        #self.captcha_list=[]
        self.captcha_passed = False
        
        
        
        
        
        
        #/html/body/main/main/div/div/div[2]/div[2]/form/input[2]        
        captchaId_element = self.WebDriverWait(chrome, 10).until(self.EC.presence_of_element_located((self.By.XPATH,"/html/body/main/main/div/div/div[2]/div[2]/form/input[2]"))) 
        captchaId_value = captchaId_element.get_attribute("value")
        print("captchId = "+ captchaId_value)  
        
        captchaData_element= self.WebDriverWait(chrome, 10).until(self.EC.presence_of_element_located((self.By.XPATH,"/html/body/main/main/div/div/div[2]/div[2]/form/input[4]"))) 
        captchaData_value = urllib.parse.quote(captchaData_element.get_attribute("value"), safe='/')  
        print("captchaData = "+ captchaData_value)  
        
        scriptData_element= self.WebDriverWait(chrome, 10).until(self.EC.presence_of_element_located((self.By.XPATH,"/html/body/main/main/div/div/div[2]/div[2]/form/input[5]"))) 
        scriptData_value =  urllib.parse.quote(scriptData_element.get_attribute("value"), safe='/') 
        print("scriptData = "+ scriptData_value)  
        
        tokenVerification_element= self.WebDriverWait(chrome, 10).until(self.EC.presence_of_element_located((self.By.XPATH,"/html/body/main/main/div/div/div[2]/div[2]/form/input[6]"))) 
        tokenVerification_value = urllib.parse.quote(tokenVerification_element.get_attribute("value"), safe='/') 
        print("tokenVerification = "+ tokenVerification_value) 
        print(list_input)
        list_input[0]-=1
        list_input[1]-=11
        
        print(list_input)  
        if self.login_all:
            for i in self.data:   		   
        	    self.update_cookies_for_user(i,chrome,list_input,
        	captchaId_value,captchaData_value,scriptData_value,tokenVerification_value)
        else:
            self.update_cookies_for_user(user,chrome,list_input,
        	captchaId_value,captchaData_value,scriptData_value,tokenVerification_value)
        #try:
        with open('cookies.json', 'w') as f3:
            self.json.dump(self.Cookies, f3)
        #except Exception as e:
                #print(f"An error occurred: {str(e)}")
        cookies = chrome.get_cookies()

        # Nouvelle étape : Validation du type de demande selon les règles 2025
        self.logger.info(f"Validation du type de demande pour {user}")
        validation_success = self.validate_application_type(chrome, user)
        if not validation_success:
            self.logger.warning(f"Échec de la validation du type de demande pour {user}")

    def update_cookies_for_user(self,user_idd,chrome,list_input,captchaId_value,captchaData_value,scriptData_value,tokenVerification_value):
        import http.client
        import re
        import subprocess
        import urllib.parse
        import threading
        cookies = chrome.get_cookies()
        list_cookies=""
        for cookie_value in cookies:
            list_cookies+= f"{cookie_value['name']}={cookie_value['value']}; "
# Print the cookies
        user_id=self.data[user_idd][0]
        password = self.data[user_idd][1] # Get password from data.json
        for cookie in cookies:
            print(f"Name: {cookie['name']}")
            print(f"Value: {cookie['value']}")
            #print(f"Domain: {cookie['domain']}") 
        
        user_password_id_list=""  
        #_list=""  
        ll =list_input[1]
        for n in range(1,11):
            if n==list_input[0]:
                the_added1= "UserId"+str(list_input[0])+f"={user_id}&"
            else :
                the_added1 = "UserId"+str(n)+"=&"
            
            user_password_id_list+=the_added1
            
        
        #print(ll)    
        for n in range(1,11):
            
            if n==list_input[1]:
                print(list_input[1])
                the_added2= "Password"+str(list_input[1])+f"={password}&"
            else :
                the_added2 = "Password"+str(n)+"=&"
            
            user_password_id_list+=the_added2
        formatted_string = f"{user_password_id_list}ReturnUrl=&CaptchaId={captchaId_value}&CaptchaParam=&CaptchaData={captchaData_value}&ScriptData={scriptData_value}&__RequestVerificationToken={tokenVerification_value}&X-Requested-With=XMLHttpRequest"
        the_post_str = str(formatted_string)
        
        print(formatted_string)
        curl_command = f"curl -X POST -H 'Cookie: {list_cookies}' -H 'User-Agent: Google Chrome/112.0.5615.49 Linux' -H 'Origin: https://algeria.blsspainglobal.com'-H 'Referer: https://algeria.blsspainglobal.com/DZA/Account/LogIn?ReturnUrl=%2FDZA%2Fbls%2FVisaTypeVerification' -d '{formatted_string}' https://algeria.blsspainglobal.com/DZA/account/loGinPost -i"
        print(curl_command)
        import io
        # Execute the curl command using subprocess
        
        result = subprocess.run(curl_command, shell=True, check=True, text=True, capture_output=True)
        
        set_cookie_headers = re.findall(r'set-cookie:(.*?)\n', result.stdout, re.I)
        print('set_cookie_headers')
        print(result.stdout)
        #print(set_cookie_headers)

        cookies = {}
        for header in set_cookie_headers:
            header = header.strip()
            cookie_attributes = header.split(';')

            # Split the first attribute into name and value
            name, value = cookie_attributes[0].strip().split('=', 1) if '=' in cookie_attributes[0] else (cookie_attributes[0].strip(), '')

            # Add the cookie to the dictionary
            cookies[name] = value
            
        for cookie_name, cookie_value in cookies.items():
            print(f"Cookie Name: {cookie_name}")
            print(f"Cookie Value: {cookie_value}")
            try:
                self.cookie = {
        "name'": cookie_name,
        "value": cookie_value,
        "domain": "algeria.blsspainglobal.com" 
    }  
            except:
                pass
        try:
            self.Cookies[user_idd]= {
        "name": cookie_name,
        "value": cookie_value,
        "domain": "algeria.blsspainglobal.com"}
            self.loggedIn = True
        except:
            pass
        
            
        
        
        
        
    def ultra_fast_login(self, chrome, user):
        """
        Login VRAIMENT ULTRA-RAPIDE : injection + clic en 1 seconde max
        """
        try:
            # Obtenir l'email de l'utilisateur
            user_email = self.data[user][0] if user in self.data else user

            # Script SIMPLE et RAPIDE
            simple_fast_script = f"""
            // SIMPLE ET RAPIDE - Pas de boucles compliquées
            setTimeout(() => {{
                // Chercher le premier champ input visible
                const inputs = document.querySelectorAll('input[type="text"], input[type="email"]');
                for (let input of inputs) {{
                    if (input.offsetParent !== null && !input.disabled) {{
                        console.log('RAPIDE: Email injecté');
                        input.value = '{user_email}';
                        input.dispatchEvent(new Event('input', {{ bubbles: true }}));

                        // Chercher bouton Verify et cliquer IMMÉDIATEMENT
                        setTimeout(() => {{
                            const buttons = document.querySelectorAll('button');
                            for (let btn of buttons) {{
                                if (btn.textContent.toLowerCase().includes('verify')) {{
                                    console.log('RAPIDE: Bouton cliqué');
                                    btn.click();
                                    break;
                                }}
                            }}
                        }}, 100);
                        break;
                    }}
                }}
            }}, 500); // Attendre 500ms pour que la page soit prête
            """

            # Exécuter le script SIMPLE
            chrome.execute_script(simple_fast_script)
            self.logger.info(f"Script SIMPLE exécuté pour {user_email}")

        except Exception as e:
            self.logger.error(f"Erreur script simple: {e}")

    def inject_email_immediately_simple(self, chrome, user):
        """
        Injection email ULTRA-SIMPLE et IMMÉDIATE - ÉTAPE 1 optimisée
        """
        try:
            self.logger.info("INJECTION EMAIL ULTRA-SIMPLE - ÉTAPE 1")

            # Obtenir l'email
            user_email = self.data[user][0] if user in self.data else user
            user_password = self.data[user][1] if user in self.data and len(self.data[user]) > 1 else ""

            # Script ULTRA-SIMPLE pour ÉTAPE 1 + ÉTAPE 2
            simple_script = f"""
            console.log('ULTRA-SIMPLE: Démarrage 2 étapes');

            // ÉTAPE 1: Email + Verify
            const emailInputs = document.querySelectorAll('input[type="text"], input[type="email"]');
            for (let input of emailInputs) {{
                if (input.offsetParent !== null && !input.disabled) {{
                    input.value = '{user_email}';
                    input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                    console.log('ÉTAPE 1: Email injecté');

                    // Cliquer Verify
                    setTimeout(() => {{
                        const verifyBtns = document.querySelectorAll('button');
                        for (let btn of verifyBtns) {{
                            if (btn.textContent.toLowerCase().includes('verify')) {{
                                btn.click();
                                console.log('ÉTAPE 1: Verify cliqué');

                                // ÉTAPE 2: Mot de passe après redirection
                                setTimeout(() => {{
                                    console.log('ÉTAPE 2: Recherche mot de passe...');
                                    const passwordInputs = document.querySelectorAll('input[type="password"]');
                                    for (let pwdInput of passwordInputs) {{
                                        if (pwdInput.offsetParent !== null && !pwdInput.disabled) {{
                                            pwdInput.value = '{user_password}';
                                            pwdInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
                                            console.log('ÉTAPE 2: Mot de passe injecté');
                                            break;
                                        }}
                                    }}
                                }}, 3000); // 3s pour redirection
                                break;
                            }}
                        }}
                    }}, 100); // 100ms pour Verify
                    break;
                }}
            }}
            """

            # Exécuter le script
            chrome.execute_script(simple_script)
            self.logger.info(f"Script ULTRA-SIMPLE exécuté pour {user_email}")

            # Attendre que tout se termine
            self.time.sleep(8)

            # Vérifier captcha
            try:
                captcha_elements = chrome.find_elements(self.By.XPATH, "//*[contains(text(), 'Please select all boxes')]")
                if captcha_elements:
                    self.logger.info("Captcha détecté")
                    print("🎯 CAPTCHA DÉTECTÉ - Résolvez-le manuellement")
            except:
                pass

            self.logger.info("INJECTION ULTRA-SIMPLE terminée")

        except Exception as e:
            self.logger.error(f"Erreur injection ultra-simple: {e}")

    def ultra_fast_complete_session(self, user):
        """
        Session COMPLÈTE ULTRA-RAPIDE - BYPASS TOTAL de toute logique existante
        """
        try:
            self.logger.info(f"SESSION COMPLÈTE ULTRA-RAPIDE pour {user}")

            # Obtenir les données
            user_email = self.data[user][0] if user in self.data else user
            user_password = self.data[user][1] if user in self.data and len(self.data[user]) > 1 else ""

            # Créer Chrome directement sur la page de connexion
            chrome_options = self.webdriver.ChromeOptions()

            # Options minimales pour vitesse maximale
            login_url = "https://algeria.blsspainglobal.com/DZA/Account/LogIn?ReturnUrl=%2FDZA%2Fappointment%2Fnewappointment"
            chrome_options.add_argument(f"--app={login_url}")
            chrome_options.add_argument("--disable-web-security")
            chrome_options.add_argument("--ignore-certificate-errors")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-gpu")

            # Créer le navigateur DIRECTEMENT
            chrome = self.webdriver.Chrome(options=chrome_options)

            self.logger.info("Chrome créé directement sur page de connexion")

            # Attendre 1 seconde seulement
            self.time.sleep(1)

            # Script JavaScript COMPLET pour les 2 étapes
            complete_script = f"""
            console.log('ULTRA-RAPIDE COMPLET: Démarrage');

            // ÉTAPE 1: Email + Verify
            setTimeout(() => {{
                const emailInputs = document.querySelectorAll('input[type="text"], input[type="email"]');
                for (let input of emailInputs) {{
                    if (input.offsetParent !== null && !input.disabled) {{
                        input.value = '{user_email}';
                        input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                        console.log('ULTRA-RAPIDE: Email injecté');

                        // Cliquer Verify immédiatement
                        setTimeout(() => {{
                            const verifyBtns = document.querySelectorAll('button');
                            for (let btn of verifyBtns) {{
                                if (btn.textContent.toLowerCase().includes('verify')) {{
                                    btn.click();
                                    console.log('ULTRA-RAPIDE: Verify cliqué');

                                    // ÉTAPE 2: Mot de passe après redirection
                                    setTimeout(() => {{
                                        console.log('ULTRA-RAPIDE: Recherche mot de passe...');
                                        const passwordInputs = document.querySelectorAll('input[type="password"]');
                                        for (let pwdInput of passwordInputs) {{
                                            if (pwdInput.offsetParent !== null && !pwdInput.disabled) {{
                                                pwdInput.value = '{user_password}';
                                                pwdInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
                                                console.log('ULTRA-RAPIDE: Mot de passe injecté');
                                                break;
                                            }}
                                        }}
                                    }}, 2000); // 2s pour redirection
                                    break;
                                }}
                            }}
                        }}, 100); // 100ms pour Verify
                        break;
                    }}
                }}
            }}, 300); // 300ms pour email
            """

            # Exécuter le script
            chrome.execute_script(complete_script)
            self.logger.info("Script ULTRA-RAPIDE COMPLET exécuté")

            # Attendre 8 secondes pour que tout se termine
            self.time.sleep(8)

            # Vérifier et résoudre captcha automatiquement
            try:
                captcha_elements = chrome.find_elements(self.By.XPATH, "//*[contains(text(), 'Please select all boxes')]")
                if captcha_elements:
                    self.logger.info("ULTRA-RAPIDE: Captcha détecté - Résolution automatique")
                    print("🎯 CAPTCHA DÉTECTÉ - Résolution automatique avec imagetotext.cc")

                    # Résoudre automatiquement le captcha
                    captcha_solved = self.solve_captcha_with_imagetotext(chrome)

                    if captcha_solved:
                        self.logger.info("ULTRA-RAPIDE: Captcha résolu automatiquement")
                        print("✅ CAPTCHA RÉSOLU AUTOMATIQUEMENT - Connexion réussie !")

                        # Attendre que la connexion se termine
                        self.time.sleep(3)
                    else:
                        self.logger.warning("ULTRA-RAPIDE: Échec résolution automatique captcha")
                        print("⚠️ Échec résolution automatique - Résolvez manuellement")
                else:
                    self.logger.info("ULTRA-RAPIDE: Pas de captcha détecté")
            except Exception as e:
                self.logger.error(f"Erreur vérification captcha: {e}")

            self.logger.info("SESSION COMPLÈTE ULTRA-RAPIDE terminée")

        except Exception as e:
            self.logger.error(f"Erreur session ultra-rapide complète: {e}")

    def solve_captcha_with_imagetotext(self, chrome):
        """
        Résout automatiquement le captcha en utilisant imagetotext.cc
        """
        try:
            self.logger.info("Résolution automatique du captcha avec imagetotext.cc")

            # Prendre une capture d'écran du captcha
            captcha_screenshot = chrome.get_screenshot_as_png()

            # Sauvegarder temporairement
            import tempfile
            import os
            with tempfile.NamedTemporaryFile(delete=False, suffix='.png') as tmp_file:
                tmp_file.write(captcha_screenshot)
                screenshot_path = tmp_file.name

            # Utiliser imagetotext.cc pour extraire le texte
            import requests

            # Utiliser OCR.space API (gratuite)

            # Préparer la requête pour OCR.space
            with open(screenshot_path, 'rb') as f:
                response = requests.post(
                    'https://api.ocr.space/parse/image',
                    files={'file': f},
                    data={
                        'apikey': 'helloworld',  # Clé API gratuite
                        'language': 'eng',
                        'isOverlayRequired': False,
                        'detectOrientation': False,
                        'scale': True,
                        'OCREngine': 2
                    }
                )

            if response.status_code == 200:
                result = response.json()
                if result.get('IsErroredOnProcessing', False):
                    self.logger.error(f"Erreur OCR: {result.get('ErrorMessage', 'Erreur inconnue')}")
                    return False

                # Extraire le texte de la réponse OCR.space
                parsed_results = result.get('ParsedResults', [])
                if parsed_results:
                    extracted_text = parsed_results[0].get('ParsedText', '')
                    self.logger.info(f"Texte extrait du captcha: {extracted_text}")
                else:
                    self.logger.error("Aucun texte extrait du captcha")
                    return False

                # Analyser le texte pour trouver la consigne et les nombres
                import re

                # Chercher la consigne "Please select all boxes with number XXX"
                target_match = re.search(r'select all boxes with number (\d+)', extracted_text, re.IGNORECASE)
                if target_match:
                    target_number = target_match.group(1)
                    self.logger.info(f"Nombre cible détecté: {target_number}")

                    # Chercher tous les nombres dans la grille
                    all_numbers = re.findall(r'\b\d{3}\b', extracted_text)
                    self.logger.info(f"Nombres trouvés dans la grille: {all_numbers}")

                    # Trouver les positions des cases contenant le nombre cible
                    target_positions = []
                    for i, number in enumerate(all_numbers):
                        if number == target_number:
                            target_positions.append(i)

                    self.logger.info(f"Positions à cliquer: {target_positions}")

                    # Cliquer sur les cases correspondantes
                    self.click_captcha_boxes(chrome, target_positions)

                    # Cliquer sur Submit
                    self.click_captcha_submit(chrome)

                    return True
                else:
                    self.logger.error("Impossible de trouver le nombre cible dans le texte")
                    return False
            else:
                self.logger.error(f"Erreur API OCR.space: {response.status_code}")
                return False

        except Exception as e:
            self.logger.error(f"Erreur résolution captcha automatique: {e}")
            return False
        finally:
            # Nettoyer le fichier temporaire
            try:
                os.unlink(screenshot_path)
            except:
                pass

    def click_captcha_boxes(self, chrome, positions):
        """
        Clique sur les cases du captcha aux positions spécifiées
        """
        try:
            # Script JavaScript pour cliquer sur les cases
            click_script = f"""
            console.log('Clic automatique sur les cases du captcha');

            // Chercher toutes les cases cliquables du captcha
            const captchaBoxes = document.querySelectorAll('div[onclick], td[onclick], img[onclick], button[data-value]');

            const positions = {positions};

            for (let pos of positions) {{
                if (captchaBoxes[pos]) {{
                    captchaBoxes[pos].click();
                    console.log('Case cliquée à la position:', pos);
                }}
            }}
            """

            chrome.execute_script(click_script)
            self.logger.info(f"Cases cliquées aux positions: {positions}")

        except Exception as e:
            self.logger.error(f"Erreur clic cases captcha: {e}")

    def click_captcha_submit(self, chrome):
        """
        Clique sur le bouton Submit du captcha
        """
        try:
            # Script JavaScript pour cliquer sur Submit
            submit_script = """
            console.log('Clic automatique sur Submit captcha');

            // Chercher le bouton Submit
            const submitButtons = document.querySelectorAll('button, input[type="submit"], input[type="button"]');

            for (let btn of submitButtons) {
                const text = (btn.textContent || btn.value || '').toLowerCase();
                if (text.includes('submit') || text.includes('verify') || text.includes('continue')) {
                    btn.click();
                    console.log('Bouton Submit cliqué');
                    break;
                }
            }
            """

            chrome.execute_script(submit_script)
            self.logger.info("Bouton Submit du captcha cliqué")

        except Exception as e:
            self.logger.error(f"Erreur clic Submit captcha: {e}")

    def direct_fast_login(self, chrome, user):
        """
        Login DIRECT et RAPIDE - 2 étapes optimisées : Email puis Mot de passe + Captcha
        """
        try:
            self.logger.info("DIRECT FAST LOGIN - 2 étapes optimisées")

            # Obtenir les données utilisateur
            user_email = self.data[user][0] if user in self.data else user
            user_password = self.data[user][1] if user in self.data and len(self.data[user]) > 1 else ""

            # ÉTAPE 1: Email + Verify (page actuelle)
            self.logger.info("ÉTAPE 1: Injection email + clic Verify")

            email_script = f"""
            console.log('ÉTAPE 1: Email + Verify');

            setTimeout(() => {{
                // Trouver champ email
                const emailInputs = document.querySelectorAll('input[type="text"], input[type="email"]');
                for (let input of emailInputs) {{
                    if (input.offsetParent !== null && !input.disabled) {{
                        input.value = '{user_email}';
                        input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                        console.log('Email injecté:', '{user_email}');

                        // Cliquer Verify immédiatement
                        setTimeout(() => {{
                            const verifyBtns = document.querySelectorAll('button');
                            for (let btn of verifyBtns) {{
                                if (btn.textContent.toLowerCase().includes('verify')) {{
                                    btn.click();
                                    console.log('Verify cliqué - Redirection vers page 2');
                                    break;
                                }}
                            }}
                        }}, 100);
                        break;
                    }}
                }}
            }}, 300);
            """

            chrome.execute_script(email_script)
            self.logger.info("Script email exécuté")

            # Attendre la redirection vers page 2 (mot de passe + captcha)
            self.time.sleep(3)

            # ÉTAPE 2: Mot de passe + Captcha (nouvelle page)
            self.logger.info("ÉTAPE 2: Injection mot de passe sur nouvelle page")

            password_script = f"""
            console.log('ÉTAPE 2: Mot de passe + Captcha');

            // Fonction pour injecter mot de passe dès que possible
            function injectPassword() {{
                const passwordInputs = document.querySelectorAll('input[type="password"]');
                for (let pwdInput of passwordInputs) {{
                    if (pwdInput.offsetParent !== null && !pwdInput.disabled) {{
                        pwdInput.value = '{user_password}';
                        pwdInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
                        console.log('Mot de passe injecté');
                        return true;
                    }}
                }}
                return false;
            }}

            // Essayer immédiatement
            if (!injectPassword()) {{
                // Retry toutes les 200ms pendant 5 secondes
                let attempts = 0;
                const interval = setInterval(() => {{
                    attempts++;
                    if (injectPassword() || attempts >= 25) {{
                        clearInterval(interval);
                        if (attempts < 25) {{
                            console.log('Mot de passe injecté après', attempts * 200, 'ms');
                        }}
                    }}
                }}, 200);
            }}
            """

            chrome.execute_script(password_script)
            self.logger.info("Script mot de passe exécuté")

            # Attendre que le mot de passe soit injecté
            self.time.sleep(2)

            # Vérifier si captcha présent
            try:
                captcha_elements = chrome.find_elements(self.By.XPATH, "//*[contains(text(), 'Please select all boxes')]")
                if captcha_elements:
                    self.logger.info("DIRECT: Captcha détecté sur page 2")
                    print("🎯 CAPTCHA DÉTECTÉ - Résolvez-le manuellement puis le bot continuera")
                else:
                    self.logger.info("DIRECT: Pas de captcha détecté")
            except:
                pass

            self.logger.info("DIRECT FAST LOGIN terminé - 2 étapes complètes")

        except Exception as e:
            self.logger.error(f"Erreur DIRECT FAST LOGIN: {e}")



    def open_bls(self,chrome,user,auto):
        import threading
        is_open = False
        while not is_open:
            print(" 1 from open bls of "+ str(self.current_user))
            # Vérifier si l'utilisateur a des cookies, sinon initialiser à None
            if user not in self.Cookies:
                self.Cookies[user] = None
                self.logger.info(f"Cookies initialisés à None pour {user}")

            print(f"Cookies pour {user}: {self.Cookies[user]}")

            print(" 2 from open bls of "+ str(user))
            #self.time.sleep(2)

            if self.all_manual  :
                # Navigation DIRECTE vers la page de connexion pour plus de rapidité
                chrome.get('https://algeria.blsspainglobal.com/DZA/Account/LogIn?ReturnUrl=%2FDZA%2Fappointment%2Fnewappointment')
                if not auto :
                    is_open = True
                return
            if self.fast_mode:
                # Mode rapide : aller DIRECTEMENT à la page de connexion avec cookies
                chrome.get('https://algeria.blsspainglobal.com/DZA/Account/LogIn?ReturnUrl=%2FDZA%2Fappointment%2Fnewappointment')
                if self.Cookies[user] is not None:
                    try:
                        chrome.add_cookie(self.Cookies[user])
                        self.logger.info(f"Cookies ajoutés en mode rapide pour {user}")
                    except Exception as cookie_error:
                        self.logger.warning(f"Erreur cookies mode rapide pour {user}: {cookie_error}")
                # Marquer comme ouvert après la navigation
                if not auto :
                    is_open = True
            
            else:
                print("the first url is ",chrome.current_url)
                try:
                    # BYPASS TOTAL - Injection email IMMÉDIATE sans aucune logique complexe
                    self.logger.info(f"BYPASS TOTAL - Injection email IMMÉDIATE pour {user}")

                    # Attendre juste que Chrome soit prêt (minimal)
                    self.time.sleep(2)

                    # INJECTION EMAIL IMMÉDIATE - sans passer par aucune autre logique
                    self.inject_email_immediately_simple(chrome, user)

                    # FORCER L'ARRÊT COMPLET - ne pas continuer
                    self.logger.info("BYPASS TOTAL terminé - ARRÊT COMPLET")
                    return  # SORTIR IMMÉDIATEMENT - AUCUNE autre logique

                    # Ajouter les cookies si disponibles
                    if self.Cookies[user] is not None:
                        try:
                            chrome.add_cookie(self.Cookies[user])
                            self.logger.info(f"Cookies ajoutés pour {user}")
                        except Exception as cookie_error:
                            self.logger.warning(f"Erreur lors de l'ajout des cookies pour {user}: {cookie_error}")

                    # Démarrer le processus de réservation automatique
                    self.start_booking_process(chrome, user)
                    self.logger.info(f"Navigation vers page principale BLS pour {user}")

                except Exception as nav_error:
                    self.logger.error(f"Erreur lors de la navigation pour {user}: {nav_error}")
                    # En cas d'erreur, essayer une navigation simple
                    chrome.get('https://algeria.blsspainglobal.com/DZA/account/login')

                # Marquer comme ouvert après la navigation
                if not auto :
                    is_open = True
                
                #if chrome.current_url != "https://algeria.blsspainglobal.com/DZA/bls/VisaTypeVerification":
                   # if self.Cookies[user] != None :
                        #try:
                            #chrome.delete_cookie('.AspNetCore.Antiforgery.cyS7zUT4rj8')
                            #chrome.delete_cookie('.AspNetCore.Cookies')
                            #chrome.add_cookie(self.Cookies[user])
                            #print("all cookies deleted") 
                       # except:
                            #chrome.add_cookie(self.Cookies[user])
                      
                        #chrome.get('https://algeria.blsspainglobal.com/DZA/bls/VisaTypeVerification')
                
                print(" 3 from open bls of "+ str(user))
                if "algeria.blsspainglobal.com/DZA" not in chrome.current_url and auto :
                    self.login(chrome,user)
                    chrome.add_cookie(self.Cookies[user])
                    self.time.sleep(2)
                    chrome.get('https://algeria.blsspainglobal.com/DZA/')
            current_u =  self.current_user   
            
            
            if auto :
                self.current_user  = current_u

            # Désactiver l'ancienne logique de captcha si on utilise le nouveau système
            if hasattr(self, 'use_new_login_system') and self.use_new_login_system:
                self.logger.info("Utilisation du nouveau système de connexion - ancienne logique désactivée")
                # Au lieu de s'arrêter, continuer avec le nouveau processus de réservation
                self.start_booking_process(chrome, user)
                is_open = True
                return

            #while self.fifo.qsize() < 3:
            #chrome.get('https://algeria.blsspainglobal.com/DZA/bls/VisaTypeVerification')
                try :
                    verify_selection = self.WebDriverWait(chrome, 10).until(self.EC.presence_of_element_located((self.By.ID, "btnVerify")))
                    verify_selection.click()
                    self.iframe = self.WebDriverWait(chrome, 10).until(self.EC.presence_of_element_located((self.By.CSS_SELECTOR,".k-widget.k-window")))

                    self.position = self.iframe.location
                    self.size = self.iframe.size
                    chrome.switch_to.frame(0)
                except:
                    try:
                        self.time.sleep(2)
                        verify_selection = self.WebDriverWait(chrome, 10).until(self.EC.presence_of_element_located((self.By.ID, "btnVerify")))
                        verify_selection.click()
                        self.iframe = self.WebDriverWait(chrome, 10).until(self.EC.presence_of_element_located((self.By.CSS_SELECTOR,".k-widget.k-window")))

                        self.position = self.iframe.location
                        self.size = self.iframe.size
                        chrome.switch_to.frame(0)
                    except:
                        # Au lieu de lever une erreur, utiliser le nouveau système
                        self.logger.warning("Ancienne logique de captcha échouée - basculement vers le nouveau système")
                        is_open = True
                        return
                
                is_open= True
                self.captcha_list=[]
                self.captcha_passed = False
               
                print('before thread')
                
                print('before while')
                while len(self.captcha_list)!=10 or self.captcha_passed == False :
                    my_thread1 = threading.Thread(target=self.get_Captcha,args=(chrome,))
                    my_thread1.start()
                    my_thread1.join()
                    print(self.captcha_list)
                    if len(self.captcha_list)!=10 and len(self.captcha_list)!=0:
                        repeat_captcha = self.WebDriverWait(chrome, 10).until(self.EC.presence_of_element_located((self.By.XPATH,"/html/body/div/div[2]/div[2]/div/form/div[2]/div[2]")))
                    
                        repeat_captcha.click()
                    if set(self.captcha_list_old)==set(self.captcha_list):
                        
                        raise MyCustomError("This is a custom error message in get captcha")
                    
                    print('in while')
                #self.which_part=1
                    
                    if len(self.captcha_list)==10 :
                        self.click_captcha(chrome,True)
                    if self.captcha_passed==True :
                        try:
                            self.remplir_formulair(chrome,user)
                        except:
                            raise MyCustomError("This is a custom error message in remplir formulaire")  
                            self.available_date  = False
                
    def get_Captcha(self,chrome):
        import urllib.parse 
        import subprocess
        from bs4 import BeautifulSoup
        import re
        
        curl_command = ""  # Initialize curl_command
        #part=self.which_part
        
        
        
        
        
        
        screenshot = chrome.get_screenshot_as_png()
        screenshot = self.Image.open(self.io.BytesIO(screenshot))
        screenshot1 = screenshot.crop((self.position['x'], self.position['y'], self.position['x'] + (self.size['width']/3), self.position['y'] + self.size['height']))
            #screenshot1.save("part1.png")
            
        screenshot2 = screenshot.crop((self.position['x']+ (self.size['width']/3), self.position['y'], self.position['x'] + 2*(self.size['width'])/3, self.position['y'] + self.size['height']))
            #screenshot2.save("part2.png")
            
        screenshot3 = screenshot.crop((self.position['x'] + 2*(self.size['width'])/3, self.position['y'], self.position['x'] + self.size['width'], self.position['y'] + self.size['height']))
            #screenshot3.save("part3.png")
            
        combined_image = self.Image.new("RGB", (self.size['width'], 3*self.size['height']))
        combined_image.paste(screenshot1, (0, 0))
        combined_image.paste(screenshot2, (0, self.size['height']))
        combined_image.paste(screenshot3, (0, 2*self.size['height']))
        combined_image.save("below.png")
        
        if self.decoded_token1 == '' :
            curl_command_init = f"curl 'https://www.imagetotext.cc/' -i"
            result = subprocess.run(curl_command_init, shell=True, check=True, text=True, capture_output=True)
            
            set_cookie_headers = re.findall(r'set-cookie:(.*?)\n', result.stdout, re.I)
            header = set_cookie_headers[0].strip()
            cookie_attributes = set_cookie_headers[0].split(';')
            name, self.token1 = cookie_attributes[0].strip().split('=', 1) if '=' in cookie_attributes[0] else (cookie_attributes[0].strip(), '')
            
            header = set_cookie_headers[1].strip()
            cookie_attributes = set_cookie_headers[1].split(';')

            name2, self.token2 = cookie_attributes[0].strip().split('=', 1) if '=' in cookie_attributes[0] else (cookie_attributes[0].strip(), '')
            
            self.decoded_token1 = urllib.parse.unquote(self.token1)
        
        
        
        
        
        
        #curl_command = "curl -X POST -F 'img=@below.png;type=image/png;filename=my_image.png' -F 'uploadurl=' -F 'submitType=1' -F 'url=' -F 'tool_submit=1' -F 'submit=1' https://www.editpad.org/tool/extract-text-from-image -i"
        curl_command= f"curl -X POST -H 'Cookie: XSRF-TOKEN={self.token1}; imagetotextcc_session={self.token2}' -F 'images=@below.png;type=image/png;filename=my_image.png' -H 'X-Xsrf-Token:{self.decoded_token1}'  https://www.imagetotext.cc/file-upload -i"
        # Execute the curl command using subprocess
        try:
            result = subprocess.run(curl_command, shell=True, check=True, text=True, capture_output=True)
            set_cookie_headers = re.findall(r'set-cookie:(.*?)\n', result.stdout, re.I)
            header = set_cookie_headers[0].strip()
            cookie_attributes = set_cookie_headers[0].split(';')
            name3, self.token1 = cookie_attributes[0].strip().split('=', 1) if '=' in cookie_attributes[0] else (cookie_attributes[0].strip(), '')
            #cookies[name3] = value3


            header = set_cookie_headers[1].strip()
            cookie_attributes = set_cookie_headers[1].split(';')

            name4, self.token2 = cookie_attributes[0].strip().split('=', 1) if '=' in cookie_attributes[0] else (cookie_attributes[0].strip(), '')
            self.decoded_token1 = urllib.parse.unquote(self.token1)
            
            parts = result.stdout.split('\n\n', 1)

# The second part contains the JSON response.
            json_response = parts[1]
            three_digit_numbers = re.findall(r'\d{3}', json_response)
            self.captch_list_old = self.captcha_list
            print("self.captch_list_old :")
            print(self.captch_list_old)
            
            self.captcha_list = three_digit_numbers
            
            print("self.captch_list")
            print(self.captcha_list)
            
        # Print the extracted three-digit numbers
                #print('part=  '+str(part))
            for number in three_digit_numbers:
                print(number)
            return three_digit_numbers
        except subprocess.CalledProcessError as e:
            print(f"Error executing curl: {e}")
            raise MyCustomError("This is a custom error message in get captcha")
            return []
            
            
    def click_captcha(self,chrome,loggedIn):
         import subprocess
         window_size = chrome.get_window_size()
         width = window_size["width"]
         
         height = window_size["height"]
         action = self.ActionChains(chrome)
         
         if self.captcha_list[6]==self.captcha_list[0] :
            action.move_by_offset((width/2)-100, (height/2)-130).click()
            action.move_by_offset((-width/2)+100, (-height/2)+130)
         if self.captcha_list[6]==self.captcha_list[1] :
            action.move_by_offset((width/2)-100, (height/2)).click()
            action.move_by_offset((-width/2)+100, (-height/2))
         if self.captcha_list[6]==self.captcha_list[2] :
            action.move_by_offset((width/2)-100, (height/2)+100).click()
            action.move_by_offset((-width/2)+100, (-height/2)-100)
         if self.captcha_list[6]==self.captcha_list[3] :
            action.move_by_offset((width/2), (height/2)-130).click()
            action.move_by_offset((-width/2), (-height/2)+130)
         if self.captcha_list[6]==self.captcha_list[4] :
            action.move_by_offset((width/2), (height/2)).click()
            action.move_by_offset((-width/2), (-height/2))
         if self.captcha_list[6]==self.captcha_list[5] :
            action.move_by_offset((width/2), (height/2)+100).click()
            action.move_by_offset((-width/2), (-height/2)-100)
         if self.captcha_list[6]==self.captcha_list[7] :
            action.move_by_offset((width/2)+100, (height/2)-130).click()
            action.move_by_offset((-width/2)-100, (-height/2)+130)
         if self.captcha_list[6]==self.captcha_list[8] :
            action.move_by_offset((width/2)+100, (height/2)).click()
            action.move_by_offset((-width/2)-100, (-height/2))
         if self.captcha_list[6]==self.captcha_list[9] :
            action.move_by_offset((width/2)+100, (height/2)+100).click()
            action.move_by_offset((-width/2)-100, (-height/2)-100)
         #action.move_by_offset(100,100).click()
         #action.move_by_offset(-200,-230).click()
         action.perform()
         submit_captcha = self.WebDriverWait(chrome, 10).until(self.EC.presence_of_element_located((self.By.XPATH,"/html/body/div/div[2]/div[2]/div/form/div[2]/div[3]")))
         submit_captcha.click()
         self.time.sleep(1)
         
         try:
            alert = chrome.switch_to.alert
            alert.accept()
            self.alert_text = alert.text
            print(f"Alert Text: {alert_text}")
            self.captcha_passed = False
         
         except:
            self.time.sleep(2)
            try:
                 print('1.0 close captcha before')
                 #self.chrome.switch_to.frame(0)
                 print(chrome.current_url)
                 iframe = self.WebDriverWait(chrome, 1).until(self.EC.presence_of_element_located((self.By.CSS_SELECTOR,".k-widget.k-window")))
                 self.captcha_passed = False
                 print('1 close captcha after')
            except:
                 try:
                     print('first line final try')
                     iframe = self.WebDriverWait(chrome, 1).until(self.EC.presence_of_element_located((self.By.CSS_SELECTOR,".k-widget.k-window")))
                     self.captcha_passed = False
                     print('last line final try')
                 except:
                     chrome.switch_to.default_content()
                     self.captcha_passed = True
            
            
            
         if loggedIn == True and self.captcha_passed == True :
             try:
                #self.chrome.switch_to.default_content()
                
                submit = self.WebDriverWait(chrome, 1).until(self.EC.presence_of_element_located((self.By.XPATH,'/html/body/main/main/div/div/div[2]/form/div[2]/button[3]')))
                    
                submit.click()
                print('btn submit clicked')
                self.captcha_passed = True
                #self.remplir_formulair()
                 
             except  :
                try:
                    submit = self.WebDriverWait(chrome, 1).until(self.EC.presence_of_element_located((self.By.XPATH,'/html/body/main/main/div/div/div[2]/form/div[2]/button[2]')))
                    submit.click()
                    print('btn submit clicked')
                    self.captcha_passed = True
                    #self.remplir_formulair() 
                 
                except  :
                    try:
                        submit = self.WebDriverWait(chrome, 1).until(self.EC.presence_of_element_located((self.By.XPATH,'/html/body/main/main/div/div/div[2]/form/div[2]/button[1]')))
                        submit.click()
                        print('btn submit clicked')
                        self.captcha_passed = True
                        #self.remplir_formulair()
                    except  :
                        self.captcha_passed = False
                        chrome.switch_to.frame(0)
                        print('btn can not be clicked')
                        #raise MyCustomError("This is a custom error message in click captcha")
                       
         if loggedIn == False and self.captcha_passed == True:
             try:
                #self.chrome.switch_to.default_content()
                
                 submit = self.WebDriverWait(chrome, 1).until(self.EC.presence_of_element_located((self.By.CSS_SELECTOR,'#btnSubmit')))
               
                 submit.click()
                 print('btn submit clicked')
                 self.captcha_passed = True
                #self.remplir_formulair()
                 
             except  :
                
                 self.captcha_passed = False
                 chrome.switch_to.frame(0)
                 print('btn can not be clicked') 
                 raise MyCustomError("This is a custom error message in remplir formulaire")
         #if self.captcha_passed==True and loggedIn == True  :
             #self.remplir_formulair(chrome,session_number)                
         
    def remplir_formulair(self,chrome,user):
        """
        Nouvelle méthode qui suit le vrai flux du site BLS selon les captures d'écran
        """
        self.logger.info(f"Début du processus de réservation pour {user}")

        try:
            # Étape 1: Vérifier si on est sur la page d'accueil
            current_url = chrome.current_url
            self.logger.info(f"URL actuelle: {current_url}")

            # Si on n'est pas sur la bonne page, naviguer vers la page d'accueil
            if "algeria.blsspainglobal.com/DZA" not in current_url:
                self.logger.info("Navigation vers la page d'accueil BLS")
                chrome.get('https://algeria.blsspainglobal.com/DZA/')
                self.time.sleep(3)

            # Étape 2: Cliquer sur "Book Now Appointment" si disponible
            try:
                book_now_btn = self.WebDriverWait(chrome, 10).until(
                    self.EC.element_to_be_clickable((self.By.XPATH, "//button[contains(text(), 'Book Now')]"))
                )
                book_now_btn.click()
                self.logger.info("Bouton 'Book Now' cliqué")
                self.time.sleep(3)
            except Exception as e:
                self.logger.warning(f"Bouton 'Book Now' non trouvé: {e}")
                # Essayer de cliquer sur "Try Again" si présent
                try:
                    try_again_btn = chrome.find_element(self.By.XPATH, "//button[contains(text(), 'Try Again')]")
                    try_again_btn.click()
                    self.logger.info("Bouton 'Try Again' cliqué")
                    self.time.sleep(3)
                except:
                    pass

            # Étape 3: Remplir le formulaire de sélection du type de visa
            self.fill_visa_selection_form(chrome, user)

        except Exception as e:
            self.logger.error(f"Erreur dans remplir_formulair pour {user}: {e}")
            raise MyCustomError(f"Erreur dans le processus de réservation: {e}")

    def fill_visa_selection_form(self, chrome, user):
        """
        Remplit le formulaire de sélection du type de visa selon les captures d'écran
        """
        try:
            self.logger.info("Remplissage du formulaire de sélection du visa")

            # Attendre que le formulaire soit chargé
            self.time.sleep(3)

            # Sélectionner "Individual" (déjà sélectionné par défaut selon les captures)
            try:
                individual_radio = chrome.find_element(self.By.XPATH, "//input[@value='Individual']")
                if not individual_radio.is_selected():
                    individual_radio.click()
                    self.logger.info("Option 'Individual' sélectionnée")
            except Exception as e:
                self.logger.warning(f"Erreur sélection Individual: {e}")

            # Sélectionner Location (Algiers)
            try:
                location_dropdown = self.WebDriverWait(chrome, 10).until(
                    self.EC.element_to_be_clickable((self.By.NAME, "location"))
                )
                location_dropdown.click()
                self.time.sleep(1)

                algiers_option = chrome.find_element(self.By.XPATH, "//option[contains(text(), 'Algiers')]")
                algiers_option.click()
                self.logger.info("Location 'Algiers' sélectionnée")
            except Exception as e:
                self.logger.warning(f"Erreur sélection location: {e}")

            # Sélectionner Visa Type
            try:
                visa_type_dropdown = self.WebDriverWait(chrome, 10).until(
                    self.EC.element_to_be_clickable((self.By.NAME, "visa_type"))
                )
                visa_type_dropdown.click()
                self.time.sleep(1)

                # Sélectionner "Visa renewal / renouvellement de visa"
                visa_renewal_option = chrome.find_element(self.By.XPATH, "//option[contains(text(), 'Visa renewal')]")
                visa_renewal_option.click()
                self.logger.info("Visa Type 'Visa renewal' sélectionné")
            except Exception as e:
                self.logger.warning(f"Erreur sélection visa type: {e}")

            # Sélectionner Visa Sub Type
            try:
                self.time.sleep(2)  # Attendre que le dropdown se mette à jour
                visa_subtype_dropdown = self.WebDriverWait(chrome, 10).until(
                    self.EC.element_to_be_clickable((self.By.NAME, "visa_sub_type"))
                )
                visa_subtype_dropdown.click()
                self.time.sleep(1)

                # Sélectionner "ALG.3"
                alg3_option = chrome.find_element(self.By.XPATH, "//option[contains(text(), 'ALG.3')]")
                alg3_option.click()
                self.logger.info("Visa Sub Type 'ALG.3' sélectionné")
            except Exception as e:
                self.logger.warning(f"Erreur sélection visa sub type: {e}")

            # Sélectionner Category
            try:
                self.time.sleep(2)
                category_dropdown = self.WebDriverWait(chrome, 10).until(
                    self.EC.element_to_be_clickable((self.By.NAME, "category"))
                )
                category_dropdown.click()
                self.time.sleep(1)

                # Sélectionner "Premium"
                premium_option = chrome.find_element(self.By.XPATH, "//option[contains(text(), 'Premium')]")
                premium_option.click()
                self.logger.info("Category 'Premium' sélectionnée")
            except Exception as e:
                self.logger.warning(f"Erreur sélection category: {e}")

            # Cliquer sur Submit
            try:
                submit_btn = self.WebDriverWait(chrome, 10).until(
                    self.EC.element_to_be_clickable((self.By.XPATH, "//button[contains(text(), 'Submit')]"))
                )
                submit_btn.click()
                self.logger.info("Formulaire soumis")
                self.time.sleep(3)

                # Après soumission, on devrait arriver sur la page de captcha
                self.handle_captcha_verification(chrome, user)

            except Exception as e:
                self.logger.error(f"Erreur lors de la soumission: {e}")
                raise

        except Exception as e:
            self.logger.error(f"Erreur dans fill_visa_selection_form: {e}")
            raise

    def handle_captcha_verification(self, chrome, user):
        """
        Gère la vérification du captcha selon les captures d'écran
        """
        try:
            self.logger.info("Gestion de la vérification captcha")

            # Attendre que la page de captcha se charge
            self.time.sleep(5)

            # Vérifier si on est sur la page de captcha
            if "Captcha Verification" in chrome.page_source:
                self.logger.info("Page de captcha détectée")

                # Ici on peut implémenter la logique de résolution du captcha
                # Pour l'instant, on attend que l'utilisateur le résolve manuellement
                if not self.all_manual:
                    self.logger.info("Attente de résolution manuelle du captcha...")
                    input("Veuillez résoudre le captcha et appuyer sur Entrée pour continuer...")

                # Après résolution du captcha, cliquer sur Submit
                try:
                    submit_btn = self.WebDriverWait(chrome, 10).until(
                        self.EC.element_to_be_clickable((self.By.XPATH, "//button[contains(text(), 'Submit')]"))
                    )
                    submit_btn.click()
                    self.logger.info("Captcha soumis")
                    self.time.sleep(3)

                    # Après le captcha, on devrait arriver sur la page de connexion
                    self.handle_login_page(chrome, user)

                except Exception as e:
                    self.logger.error(f"Erreur soumission captcha: {e}")

        except Exception as e:
            self.logger.error(f"Erreur dans handle_captcha_verification: {e}")
            raise

    def inject_email_immediately(self, chrome, user):
        """
        Injecte l'email et clique sur Verify DÈS que le champ apparaît - ULTRA-RAPIDE
        """
        try:
            self.logger.info("Injection ULTRA-RAPIDE de l'email et auto-clic")

            # Obtenir l'email de l'utilisateur
            user_email = self.data[user][0] if user in self.data else user

            # Script JavaScript ULTRA-RAPIDE pour injection + clic automatique
            ultra_fast_script = f"""
            // Fonction ULTRA-RAPIDE pour injection + clic
            function ultraFastEmailAndClick() {{
                console.log('ULTRA-FAST: Recherche champ email...');

                // Chercher TOUS les types de champs possibles
                const selectors = [
                    'input[type="text"]',
                    'input[type="email"]',
                    'input[name*="email"]',
                    'input[id*="email"]',
                    'input[placeholder*="email"]',
                    'input[placeholder*="Email"]',
                    'input:not([type="password"]):not([type="hidden"]):not([type="submit"])'
                ];

                for (let selector of selectors) {{
                    const inputs = document.querySelectorAll(selector);
                    for (let input of inputs) {{
                        if (input.offsetParent !== null && !input.disabled && !input.readOnly && input.type !== 'password') {{
                            console.log('ULTRA-FAST: Champ trouvé, injection + événements:', input);

                            // Injection email
                            input.focus();
                            input.value = '{user_email}';
                            input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                            input.dispatchEvent(new Event('change', {{ bubbles: true }}));
                            input.dispatchEvent(new Event('keyup', {{ bubbles: true }}));
                            input.blur();

                            // Chercher et cliquer IMMÉDIATEMENT sur le bouton Verify
                            setTimeout(() => {{
                                const verifyButtons = [
                                    ...document.querySelectorAll('button'),
                                    ...document.querySelectorAll('input[type="submit"]'),
                                    ...document.querySelectorAll('input[type="button"]')
                                ];

                                for (let btn of verifyButtons) {{
                                    const text = (btn.textContent || btn.value || '').toLowerCase();
                                    if (text.includes('verify') || text.includes('submit') || text.includes('continue') || text.includes('next')) {{
                                        console.log('ULTRA-FAST: Bouton trouvé, clic immédiat:', btn);
                                        btn.click();
                                        return true;
                                    }}
                                }}
                            }}, 50); // Clic après 50ms seulement

                            return true;
                        }}
                    }}
                }}
                return false;
            }}

            // Essayer IMMÉDIATEMENT
            if (ultraFastEmailAndClick()) {{
                console.log('ULTRA-FAST: Email injecté et bouton cliqué immédiatement');
            }} else {{
                // Retry ULTRA-RAPIDE toutes les 50ms pendant 3 secondes
                let attempts = 0;
                const maxAttempts = 60; // 3 secondes
                const interval = setInterval(() => {{
                    attempts++;
                    if (ultraFastEmailAndClick() || attempts >= maxAttempts) {{
                        clearInterval(interval);
                        console.log('ULTRA-FAST: Terminé après', attempts * 50, 'ms');
                    }}
                }}, 50); // Très rapide: toutes les 50ms
            }}
            """

            # Exécuter le script ULTRA-RAPIDE
            chrome.execute_script(ultra_fast_script)
            self.logger.info(f"Script ULTRA-RAPIDE exécuté pour {user_email}")

            # Attendre minimal pour que le script fasse son travail
            self.time.sleep(0.2)

        except Exception as e:
            self.logger.error(f"Erreur lors de l'injection ULTRA-RAPIDE: {e}")
            # En cas d'erreur, continuer
            self.time.sleep(0.1)

    def handle_login_page(self, chrome, user):
        """
        Gère la page de connexion selon les captures d'écran
        """
        try:
            self.logger.info("Gestion de la page de connexion")

            # Attendre minimal pour la page de connexion
            self.time.sleep(0.5)

            # Vérifier si on est sur la page de connexion
            if "Email is required" in chrome.page_source or "Email" in chrome.page_source:
                self.logger.info("Page de connexion détectée")

                # Debug: Afficher les champs de formulaire disponibles
                try:
                    inputs = chrome.find_elements(self.By.TAG_NAME, "input")
                    self.logger.info(f"Champs input trouvés: {len(inputs)}")
                    for i, inp in enumerate(inputs[:10]):  # Limiter à 10 pour éviter le spam
                        try:
                            inp_type = inp.get_attribute("type") or "text"
                            inp_name = inp.get_attribute("name") or "no-name"
                            inp_id = inp.get_attribute("id") or "no-id"
                            inp_placeholder = inp.get_attribute("placeholder") or "no-placeholder"
                            self.logger.info(f"  Input {i}: type='{inp_type}', name='{inp_name}', id='{inp_id}', placeholder='{inp_placeholder}'")
                        except:
                            pass
                except Exception as e:
                    self.logger.error(f"Erreur debug inputs: {e}")

                # Remplir l'email - Nouvelle approche pour contourner les champs générés aléatoirement
                try:
                    # Obtenir tous les champs input visibles
                    all_inputs = chrome.find_elements(self.By.TAG_NAME, "input")
                    visible_inputs = []

                    for inp in all_inputs:
                        try:
                            # Vérifier si le champ est visible et interactif
                            if (inp.is_displayed() and inp.is_enabled() and
                                inp.get_attribute("type") in ["text", "email"] and
                                inp.size["height"] > 0 and inp.size["width"] > 0):
                                visible_inputs.append(inp)
                        except:
                            continue

                    self.logger.info(f"Champs visibles trouvés: {len(visible_inputs)}")

                    # Essayer de remplir le premier champ visible (probablement l'email)
                    if len(visible_inputs) >= 1:
                        email_field = visible_inputs[0]

                        # Faire défiler vers le champ et cliquer dessus
                        chrome.execute_script("arguments[0].scrollIntoView(true);", email_field)
                        self.time.sleep(1)
                        chrome.execute_script("arguments[0].click();", email_field)
                        self.time.sleep(1)

                        # Effacer et saisir l'email
                        chrome.execute_script("arguments[0].value = '';", email_field)
                        user_email = self.data[user][0] if isinstance(self.data[user], list) else user
                        chrome.execute_script(f"arguments[0].value = '{user_email}';", email_field)

                        # Déclencher les événements
                        chrome.execute_script("arguments[0].dispatchEvent(new Event('input', { bubbles: true }));", email_field)
                        chrome.execute_script("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", email_field)

                        self.logger.info(f"Email saisi via JavaScript: {user_email}")
                    else:
                        self.logger.error("Aucun champ email visible trouvé")

                except Exception as e:
                    self.logger.error(f"Erreur saisie email: {e}")

                # Attendre minimal pour le champ mot de passe
                self.time.sleep(0.5)

                # Remplir le mot de passe - Approche améliorée
                try:
                    # Obtenir tous les champs input après avoir saisi l'email
                    all_inputs = chrome.find_elements(self.By.TAG_NAME, "input")
                    visible_inputs = []
                    password_inputs = []

                    for inp in all_inputs:
                        try:
                            inp_type = inp.get_attribute("type")
                            # Vérifier si le champ est visible et interactif
                            if (inp.is_displayed() and inp.is_enabled() and
                                inp.size["height"] > 0 and inp.size["width"] > 0):

                                if inp_type in ["text", "email"]:
                                    visible_inputs.append(inp)
                                elif inp_type == "password":
                                    password_inputs.append(inp)
                        except:
                            continue

                    self.logger.info(f"Champs texte visibles: {len(visible_inputs)}, Champs password: {len(password_inputs)}")

                    # Essayer d'abord les champs de type password
                    password_field = None
                    if len(password_inputs) > 0:
                        password_field = password_inputs[0]
                        self.logger.info("Utilisation d'un champ de type password")
                    elif len(visible_inputs) >= 2:
                        password_field = visible_inputs[1]
                        self.logger.info("Utilisation du deuxième champ texte")

                    if password_field:
                        # Faire défiler vers le champ et cliquer dessus
                        chrome.execute_script("arguments[0].scrollIntoView(true);", password_field)
                        self.time.sleep(1)
                        chrome.execute_script("arguments[0].click();", password_field)
                        self.time.sleep(1)

                        # Effacer et saisir le mot de passe
                        chrome.execute_script("arguments[0].value = '';", password_field)
                        user_password = self.data[user][1] if isinstance(self.data[user], list) else "default_password"
                        chrome.execute_script(f"arguments[0].value = '{user_password}';", password_field)

                        # Déclencher les événements
                        chrome.execute_script("arguments[0].dispatchEvent(new Event('input', { bubbles: true }));", password_field)
                        chrome.execute_script("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", password_field)

                        self.logger.info("Mot de passe saisi via JavaScript")
                    else:
                        self.logger.error("Aucun champ mot de passe trouvé")

                except Exception as e:
                    self.logger.error(f"Erreur saisie mot de passe: {e}")

                # Cliquer sur Verify (première étape)
                try:
                    # Essayer plusieurs sélecteurs pour le bouton de vérification
                    verify_btn = None
                    selectors = [
                        (self.By.XPATH, "//button[contains(text(), 'Verify')]"),
                        (self.By.XPATH, "//button[contains(text(), 'Next')]"),
                        (self.By.XPATH, "//button[contains(text(), 'Continue')]"),
                        (self.By.XPATH, "//input[@type='submit']"),
                        (self.By.XPATH, "//button[@type='submit']"),
                        (self.By.ID, "verify"),
                        (self.By.CLASS_NAME, "btn-primary")
                    ]

                    for selector in selectors:
                        try:
                            verify_btn = self.WebDriverWait(chrome, 5).until(
                                self.EC.element_to_be_clickable(selector)
                            )
                            break
                        except:
                            continue

                    if verify_btn:
                        verify_btn.click()
                        self.logger.info("Bouton Verify cliqué (première étape)")
                        self.time.sleep(5)

                        # Vérifier si on a maintenant un champ mot de passe
                        self.handle_password_step(chrome, user)
                    else:
                        self.logger.error("Bouton Verify non trouvé")

                except Exception as e:
                    self.logger.error(f"Erreur clic bouton Verify: {e}")

        except Exception as e:
            self.logger.error(f"Erreur dans handle_login_page: {e}")
            raise

    def handle_password_step(self, chrome, user):
        """
        Gère la deuxième étape : saisie du mot de passe
        """
        try:
            self.logger.info("Gestion de l'étape mot de passe")

            # Attendre minimal pour le champ mot de passe
            self.time.sleep(0.5)

            # Chercher le champ mot de passe
            password_field = None
            selectors = [
                (self.By.XPATH, "//input[@type='password']"),
                (self.By.NAME, "password"),
                (self.By.ID, "password"),
                (self.By.XPATH, "//input[contains(@placeholder, 'password')]"),
                (self.By.XPATH, "//input[contains(@placeholder, 'Password')]")
            ]

            for selector in selectors:
                try:
                    password_field = self.WebDriverWait(chrome, 3).until(
                        self.EC.presence_of_element_located(selector)
                    )
                    break
                except:
                    continue

            if password_field:
                # Faire défiler vers le champ et cliquer dessus
                chrome.execute_script("arguments[0].scrollIntoView(true);", password_field)
                self.time.sleep(0.1)
                chrome.execute_script("arguments[0].click();", password_field)
                self.time.sleep(0.1)

                # Effacer et saisir le mot de passe
                chrome.execute_script("arguments[0].value = '';", password_field)
                user_password = self.data[user][1] if isinstance(self.data[user], list) else "default_password"
                chrome.execute_script(f"arguments[0].value = '{user_password}';", password_field)

                # Déclencher les événements
                chrome.execute_script("arguments[0].dispatchEvent(new Event('input', { bubbles: true }));", password_field)
                chrome.execute_script("arguments[0].dispatchEvent(new Event('change', { bubbles: true }));", password_field)

                self.logger.info("Mot de passe saisi (deuxième étape)")

                # Vérification rapide du captcha
                self.time.sleep(0.5)

                # Vérifier s'il y a un captcha à résoudre
                if self.check_and_solve_captcha(chrome, user):
                    self.logger.info("Captcha détecté et résolu automatiquement")

                # Chercher et cliquer sur le bouton Login final
                self.time.sleep(0.2)
                self.click_final_login_button(chrome, user)
            else:
                self.logger.error("Champ mot de passe non trouvé après vérification email")
                # Continuer quand même au cas où la connexion aurait réussi
                self.continue_booking_process(chrome, user)

        except Exception as e:
            self.logger.error(f"Erreur dans handle_password_step: {e}")
            # Continuer quand même
            self.continue_booking_process(chrome, user)

    def click_final_login_button(self, chrome, user):
        """
        Clique sur le bouton de connexion final après saisie du mot de passe
        """
        try:
            # Chercher le bouton de connexion final
            login_btn = None
            selectors = [
                (self.By.XPATH, "//button[contains(text(), 'Login')]"),
                (self.By.XPATH, "//button[contains(text(), 'Sign In')]"),
                (self.By.XPATH, "//button[contains(text(), 'Submit')]"),
                (self.By.XPATH, "//input[@type='submit']"),
                (self.By.XPATH, "//button[@type='submit']"),
                (self.By.ID, "login"),
                (self.By.CLASS_NAME, "btn-primary")
            ]

            for selector in selectors:
                try:
                    login_btn = self.WebDriverWait(chrome, 5).until(
                        self.EC.element_to_be_clickable(selector)
                    )
                    break
                except:
                    continue

            if login_btn:
                login_btn.click()
                self.logger.info("Bouton Login final cliqué")
                self.time.sleep(5)

                # Après connexion complète, continuer avec le processus de réservation
                self.continue_booking_process(chrome, user)
            else:
                self.logger.error("Bouton Login final non trouvé")
                # Continuer quand même
                self.continue_booking_process(chrome, user)

        except Exception as e:
            self.logger.error(f"Erreur clic bouton Login final: {e}")
            # Continuer quand même
            self.continue_booking_process(chrome, user)

    def check_and_solve_captcha(self, chrome, user):
        """
        Vérifie s'il y a un captcha et le résout automatiquement
        """
        try:
            self.logger.info("Vérification de la présence d'un captcha")

            # Vérifier si on est sur une page avec captcha
            captcha_indicators = [
                "select all boxes",
                "Please select",
                "captcha",
                "Captcha",
                "CAPTCHA"
            ]

            page_source = chrome.page_source
            has_captcha = any(indicator in page_source for indicator in captcha_indicators)

            if has_captcha:
                self.logger.info("Captcha détecté - Démarrage de la résolution automatique")
                return self.solve_number_captcha(chrome)
            else:
                self.logger.info("Aucun captcha détecté")
                return False

        except Exception as e:
            self.logger.error(f"Erreur dans check_and_solve_captcha: {e}")
            return False

    def solve_number_captcha(self, chrome):
        """
        Résout le captcha de sélection de nombres
        """
        try:
            self.logger.info("Tentative de résolution du captcha de nombres")

            # Chercher le texte de consigne
            instruction_text = ""
            try:
                # Différents sélecteurs possibles pour la consigne
                instruction_selectors = [
                    "//text()[contains(., 'select all boxes')]",
                    "//*[contains(text(), 'select all boxes')]",
                    "//*[contains(text(), 'Please select')]"
                ]

                for selector in instruction_selectors:
                    try:
                        element = chrome.find_element(self.By.XPATH, selector)
                        instruction_text = element.text
                        break
                    except:
                        continue

                if not instruction_text:
                    instruction_text = chrome.page_source

            except Exception as e:
                self.logger.warning(f"Impossible de récupérer la consigne: {e}")
                instruction_text = chrome.page_source

            self.logger.info(f"Consigne captcha: {instruction_text[:100]}...")

            # Extraire le nombre à chercher
            import re
            number_match = re.search(r"number['\s]*['\"]?(\d+)['\"]?", instruction_text)
            if number_match:
                target_number = number_match.group(1)
                self.logger.info(f"Nombre cible détecté: {target_number}")

                # Chercher toutes les cases du captcha (les vraies cases avec les nombres)
                captcha_boxes = []
                box_selectors = [
                    # Sélecteurs spécifiques pour les cases de captcha BLS
                    "//table//td[contains(@onclick, 'check')]",
                    "//div[contains(@onclick, 'check')]",
                    "//td[@onclick]",
                    "//div[@onclick and string-length(text()) < 10]",  # Cases courtes (nombres)
                    f"//td[text()='{target_number}']",
                    f"//div[text()='{target_number}']",
                    "//table//td[text()]",  # Toutes les cellules avec du texte
                    "//div[contains(@style, 'cursor: pointer')]"
                ]

                for selector in box_selectors:
                    try:
                        boxes = chrome.find_elements(self.By.XPATH, selector)
                        if boxes:
                            # Filtrer pour ne garder que les cases avec des nombres courts
                            filtered_boxes = []
                            for box in boxes:
                                try:
                                    text = box.text.strip()
                                    # Ne garder que les éléments avec du texte court (probablement des nombres)
                                    if text and len(text) <= 5 and text.isdigit():
                                        filtered_boxes.append(box)
                                except:
                                    pass

                            if filtered_boxes:
                                captcha_boxes.extend(filtered_boxes)
                                self.logger.info(f"Trouvé {len(filtered_boxes)} vraies cases avec sélecteur: {selector}")
                    except:
                        continue

                # Si aucune case trouvée, essayer une approche plus large
                if not captcha_boxes:
                    try:
                        all_elements = chrome.find_elements(self.By.XPATH, "//*[text()]")
                        for element in all_elements:
                            try:
                                text = element.text.strip()
                                if text and len(text) <= 5 and text.isdigit():
                                    captcha_boxes.append(element)
                            except:
                                pass
                        self.logger.info(f"Trouvé {len(captcha_boxes)} cases par recherche large")
                    except:
                        pass

                # Cliquer sur les cases contenant le nombre cible
                clicked_count = 0
                for i, box in enumerate(captcha_boxes):
                    try:
                        # Essayer différentes méthodes pour récupérer le texte
                        box_text = ""

                        # Méthode 1: .text direct
                        if box.text.strip():
                            box_text = box.text.strip()

                        # Méthode 2: innerHTML
                        if not box_text:
                            try:
                                box_text = chrome.execute_script("return arguments[0].innerHTML;", box).strip()
                                # Nettoyer le HTML
                                import re
                                box_text = re.sub(r'<[^>]+>', '', box_text).strip()
                            except:
                                pass

                        # Méthode 3: textContent
                        if not box_text:
                            try:
                                box_text = chrome.execute_script("return arguments[0].textContent;", box).strip()
                            except:
                                pass

                        # Méthode 4: Chercher dans les éléments enfants
                        if not box_text:
                            try:
                                child_elements = box.find_elements(self.By.XPATH, ".//*")
                                for child in child_elements:
                                    if child.text.strip():
                                        box_text = child.text.strip()
                                        break
                            except:
                                pass

                        self.logger.info(f"Case {i}: texte='{box_text}'")

                        if box_text == target_number:
                            chrome.execute_script("arguments[0].click();", box)
                            clicked_count += 1
                            self.logger.info(f"✅ Cliqué sur case {i} avec nombre {target_number}")
                            self.time.sleep(0.5)

                    except Exception as e:
                        self.logger.warning(f"Erreur clic sur case {i}: {e}")

                if clicked_count > 0:
                    self.logger.info(f"Captcha résolu: {clicked_count} cases cliquées")

                    # Chercher et cliquer sur le bouton Submit
                    submit_selectors = [
                        "//button[contains(text(), 'Submit')]",
                        "//input[@type='submit']",
                        "//button[@type='submit']",
                        "//*[contains(@class, 'submit')]"
                    ]

                    for selector in submit_selectors:
                        try:
                            submit_btn = chrome.find_element(self.By.XPATH, selector)
                            chrome.execute_script("arguments[0].click();", submit_btn)
                            self.logger.info("Bouton Submit cliqué")
                            self.time.sleep(2)
                            return True
                        except:
                            continue

                    self.logger.warning("Bouton Submit non trouvé")
                    return True  # Considérer comme réussi même sans Submit
                else:
                    self.logger.error(f"Aucune case trouvée avec le nombre {target_number}")
                    return False
            else:
                self.logger.error("Impossible d'extraire le nombre cible de la consigne")
                return False

        except Exception as e:
            self.logger.error(f"Erreur dans solve_number_captcha: {e}")
            return False

    def continue_booking_process(self, chrome, user):
        """
        Continue le processus de réservation après connexion
        """
        try:
            self.logger.info("Continuation du processus de réservation")

            # Attendre que la page suivante se charge
            self.time.sleep(5)

            # Ici on peut implémenter la suite du processus selon les besoins
            # Pour l'instant, on log juste le succès
            self.logger.info(f"Processus de connexion terminé pour {user}")

        except Exception as e:
            self.logger.error(f"Erreur dans continue_booking_process: {e}")
            raise

    def start_booking_process(self, chrome, user):
        """
        Démarre le processus de réservation automatique
        """
        try:
            self.logger.info(f"Démarrage du processus de réservation automatique pour {user}")

            # Étape 1: Vérifier si on est sur la page d'accueil
            current_url = chrome.current_url
            self.logger.info(f"URL actuelle: {current_url}")

            # Étape 2: Chercher et cliquer sur le bouton de réservation
            try:
                # Attendre que la page se charge complètement
                self.time.sleep(5)

                # Chercher le bouton "Book Now" ou "Try Again"
                book_now_btn = None
                try:
                    book_now_btn = self.WebDriverWait(chrome, 10).until(
                        self.EC.element_to_be_clickable((self.By.XPATH, "//button[contains(text(), 'Book Now')]"))
                    )
                    self.logger.info("Bouton 'Book Now' trouvé")
                except:
                    try:
                        book_now_btn = self.WebDriverWait(chrome, 5).until(
                            self.EC.element_to_be_clickable((self.By.XPATH, "//button[contains(text(), 'Try Again')]"))
                        )
                        self.logger.info("Bouton 'Try Again' trouvé")
                    except:
                        # Chercher d'autres variantes
                        try:
                            book_now_btn = chrome.find_element(self.By.XPATH, "//a[contains(@href, 'appointment')]")
                            self.logger.info("Lien appointment trouvé")
                        except:
                            self.logger.warning("Aucun bouton de réservation trouvé, navigation directe")
                            chrome.get('https://algeria.blsspainglobal.com/DZA/appointment/newappointment')
                            self.time.sleep(3)
                            return

                if book_now_btn:
                    book_now_btn.click()
                    self.logger.info("Bouton de réservation cliqué")
                    self.time.sleep(3)

                    # Vérifier l'URL après le clic
                    new_url = chrome.current_url
                    self.logger.info(f"Nouvelle URL après clic: {new_url}")

                    # Si on n'est pas arrivé sur la page de réservation, naviguer directement
                    if "appointment" not in new_url.lower():
                        self.logger.info("Navigation directe vers la page de réservation")
                        chrome.get('https://algeria.blsspainglobal.com/DZA/appointment/newappointment')
                        self.time.sleep(3)

            except Exception as e:
                self.logger.error(f"Erreur lors de la recherche du bouton: {e}")
                # En cas d'erreur, naviguer directement
                chrome.get('https://algeria.blsspainglobal.com/DZA/appointment/newappointment')
                self.time.sleep(3)

            # Étape 3: Continuer avec le processus de réservation
            self.continue_with_appointment_form(chrome, user)

        except Exception as e:
            self.logger.error(f"Erreur dans start_booking_process: {e}")
            raise

    def continue_with_appointment_form(self, chrome, user):
        """
        Continue avec le formulaire de réservation
        """
        try:
            current_url = chrome.current_url
            self.logger.info(f"Continuation du processus sur: {current_url}")

            # Vérifier si on est sur la page de connexion
            if "login" in current_url.lower() or "account" in current_url.lower():
                self.logger.info("Page de connexion détectée - Gestion de la connexion")
                self.handle_login_page(chrome, user)

            # Vérifier si on est sur la page de formulaire de réservation
            elif "appointment" in current_url.lower() or "newappointment" in current_url.lower():
                self.logger.info("Page de formulaire de réservation détectée")
                self.fill_visa_selection_form(chrome, user)

            else:
                self.logger.warning(f"Page inattendue: {current_url}")
                # Essayer de naviguer vers la page de formulaire
                self.logger.info("Tentative de navigation directe vers la page de réservation")
                chrome.get('https://algeria.blsspainglobal.com/DZA/appointment/newappointment')
                self.time.sleep(3)

                # Vérifier à nouveau après navigation
                new_url = chrome.current_url
                if "login" in new_url.lower():
                    self.logger.info("Redirection vers connexion après navigation")
                    self.handle_login_page(chrome, user)
                else:
                    self.fill_visa_selection_form(chrome, user)

        except Exception as e:
            self.logger.error(f"Erreur dans continue_with_appointment_form: {e}")
            # En mode automatique, on peut continuer malgré les erreurs
            pass


    async def send_telegram_message(self, the_message):
        # Replace 'YOUR_BOT_TOKEN' with the actual API token of your bot
        bot_token = '6642510643:AAHaO5qNLpdEL1iD4Fhvc4gtqoSK-SAF3JA'

        # Create a bot instance
        bot = Bot(token=bot_token)

        # Replace 'YOUR_CHAT_ID' with the chat ID of the user or group where you want to send the message
        chat_id = '-4001073822'

        # Define an async function to send the message
        
        
        await bot.send_message(chat_id=chat_id, text=the_message)

        

        # Run the async function within an event loop
        
    async def send_telegram_message_with_screenshoot(self, the_message,chrome):
        # Replace 'YOUR_BOT_TOKEN' with the actual API token of your bot
        bot_token = '6642510643:AAHaO5qNLpdEL1iD4Fhvc4gtqoSK-SAF3JA'

        # Create a bot instance
        bot = Bot(token=bot_token)

        # Replace 'YOUR_CHAT_ID' with the chat ID of the user or group where you want to send the message
        chat_id = '@bls_alerts_of'

        # Define an async function to send the message
        
        screenshot = chrome.get_screenshot_as_png()
        
        await bot.send_message(chat_id=chat_id, text=the_message)

        await bot.send_photo(chat_id=chat_id, photo=InputFile(screenshot))
